using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net.Http;
using System.Reflection;
using System.Threading.Tasks;

namespace CyberShield.ResourceTracker {
	public class SystemInfo {
		private static readonly HttpClient _httpClient = new HttpClient();
		private static SystemInfoData _cachedInfo;
		private static readonly object _lock = new object();

		public static async Task<SystemInfoData> InitializeAndCacheSystemInfoData() {
			if (_cachedInfo != null)
				return _cachedInfo;

			lock (_lock) {
				if (_cachedInfo != null)
					return _cachedInfo;

				_cachedInfo = new SystemInfoData {
					ProgramInfo = GetProgramInfo(),
					StartTime = DateTime.Now,
					ComputerName = GetComputerName(),
					Username = GetUsername(),
					OSVersion = GetOSVersion(),
					AntivirusList = GetInstalledAntivirus()
				};
			}

			// IP адрес получаем отдельно, так как это асинхронная операция
			_cachedInfo.IpAddress = await GetExternalIpAddress();
			var locationInfo = await GetLocationByIp(_cachedInfo.IpAddress);
			_cachedInfo.Country = locationInfo.Country;
			_cachedInfo.City = locationInfo.City;
			return _cachedInfo;
		}

		private static string GetProgramInfo() {
			try {
				var assembly = Assembly.GetExecutingAssembly();
				var name = assembly.GetName().Name;
				var version = assembly.GetName().Version;
				return $"{name} v{version}";
			} catch (Exception) {
				return "Неизвестная программа";
			}
		}

		private static string GetComputerName() {
			try {
				return Environment.MachineName ?? "Неизвестный компьютер";
			} catch (Exception) {
				return "Неизвестный компьютер";
			}
		}

		private static string GetUsername() {
			try {
				// попробуем получить через Environment
				string username = Environment.UserName;

				// если не удалось, попробуем через WMI
				if (string.IsNullOrEmpty(username)) {
					using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem")) {
						using (var results = searcher.Get()) {
							foreach (var result in results) {
								username = result["UserName"]?.ToString();
								break;
							}
						}
					}
				}

				return username ?? "Неизвестный пользователь";
			} catch (Exception) {
				return "Неизвестный пользователь";
			}
		}

		private static async Task<string> GetExternalIpAddress() {
			try {
				var response = await _httpClient.GetStringAsync("https://api.ipify.org");
				return response.Trim();
			} catch (Exception) {
				return "Не удалось получить IP адрес";
			}
		}

		private static async Task<(string Country, string City)> GetLocationByIp(string ipAddress) {
			try {
				if (ipAddress == "Не удалось получить IP адрес")
					return ("Неизвестная страна", "Неизвестный город");

				var response = await _httpClient.GetStringAsync($"http://ip-api.com/json/{ipAddress}");

				// Проверка статуса
				string status = ExtractJsonValue(response, "status");
				if (status != "success")
					return ("Неизвестная страна", "Неизвестный город");

				// Получение страны и города
				string country = ExtractJsonValue(response, "country");
				string city = ExtractJsonValue(response, "city");

				return (
					country?.Trim('"') ?? "Неизвестная страна",
					city?.Trim('"') ?? "Неизвестный город"
				);
			} catch (Exception) {
				return ("Неизвестная страна", "Неизвестный город");
			}
		}

		private static string GetOSVersion() {
			try {
				var osInfo = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
				var os = osInfo.Get().Cast<ManagementObject>().FirstOrDefault();
				return os?["Caption"]?.ToString() ?? "Неизвестная ОС";
			} catch {
				return Environment.OSVersion.ToString();
			}
		}

		private static List<string> GetInstalledAntivirus() {
			var antivirusList = new List<string>();
			try {
				// проверка через Windows Security Center
				using (var searcher = new ManagementObjectSearcher(@"root\SecurityCenter2", "SELECT * FROM AntivirusProduct")) {
					var searcherData = searcher.Get();
					foreach (var item in searcherData) {
						antivirusList.Add(item["displayName"].ToString());
					}
				}
			} catch {
				// если не удалось получить через WMI, проверяем реестр
				try {
					string[] regPaths = {
						@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
						@"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
					};

					var antivirusKeywords = new[] { "antivirus", "security", "антивирус", "защита", "антивірус", "захист" };

					foreach (string regPath in regPaths) {
						using (var key = Registry.LocalMachine.OpenSubKey(regPath)) {
							if (key != null) {
								foreach (string subKeyName in key.GetSubKeyNames()) {
									using (var subKey = key.OpenSubKey(subKeyName)) {
										string displayName = subKey?.GetValue("DisplayName") as string;
										if (!string.IsNullOrEmpty(displayName) &&
											antivirusKeywords.Any(k => displayName.ToLower().Contains(k))) {
											antivirusList.Add(displayName);
										}
									}
								}
							}
						}
					}
				} catch {
					antivirusList.Add("Не удалось определить антивирус");
				}
			}

			return antivirusList.Distinct().ToList();
		}

		private static string ExtractJsonValue(string json, string key) {
			key = $"\"{key}\"";
			int keyIndex = json.IndexOf(key);
			if (keyIndex == -1) return null;

			// Находим начало значения после ":"
			int valueStartIndex = json.IndexOf(':', keyIndex) + 1;
			while (valueStartIndex < json.Length && char.IsWhiteSpace(json[valueStartIndex]))
				valueStartIndex++;

			// Определяем тип значения (строка или число)
			char firstChar = json[valueStartIndex];
			if (firstChar == '"') {
				// Парсинг строкового значения
				int valueEndIndex = json.IndexOf('"', valueStartIndex + 1);
				return json.Substring(valueStartIndex + 1, valueEndIndex - valueStartIndex - 1);
			} else if (firstChar == '{' || firstChar == '[') {
				// Поддержка вложенных объектов/массивов
				int bracketCount = 1;
				int valueEndIndex = valueStartIndex + 1;
				while (valueEndIndex < json.Length && bracketCount > 0) {
					if (json[valueEndIndex] == '{' || json[valueEndIndex] == '[') bracketCount++;
					if (json[valueEndIndex] == '}' || json[valueEndIndex] == ']') bracketCount--;
					valueEndIndex++;
				}
				return json.Substring(valueStartIndex, valueEndIndex - valueStartIndex);
			} else {
				// Парсинг простых значений (число, null, boolean)
				int valueEndIndex = json.IndexOfAny(new[] { ',', '}', ']' }, valueStartIndex);
				return json.Substring(valueStartIndex, valueEndIndex - valueStartIndex).Trim();
			}
		}
	}
}
