using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CyberShield.Converters {
	public class BoolToStartupColorConverter : IValueConverter {
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture) {
			if (value is bool isInStartup) {
				// Зеленый цвет, если программа в автозагрузке, красный - если нет
				return isInStartup ? new SolidColorBrush(Color.FromRgb(76, 175, 80)) : new SolidColorBrush(Color.FromRgb(244, 67, 54));
			}
			return new SolidColorBrush(Color.FromRgb(244, 67, 54)); // По умолчанию красный
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
			throw new NotImplementedException();
		}
	}
}
