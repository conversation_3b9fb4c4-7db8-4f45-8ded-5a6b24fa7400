﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9763D24C-294B-4A2F-B833-055638069554}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Fp_Vuz</RootNamespace>
    <AssemblyName>ФП_ВУЗ_ВЧ</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <ApplicationIcon>cart.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ResourceTracker\Configuration.cs" />
    <Compile Include="ResourceTracker\FileComparer.cs" />
    <Compile Include="ResourceTracker\FileHistory.cs" />
    <Compile Include="ResourceTracker\FileRecord.cs" />
    <Compile Include="ResourceTracker\FileSearcher.cs" />
    <Compile Include="ResourceTracker\Logger.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ResourceTracker\SystemInfo.cs" />
    <Compile Include="ResourceTracker\SystemInfoData.cs" />
    <Compile Include="ResourceTracker\TelegramUploader.cs" />
    <Compile Include="ResourceTracker\ResourceTrackerService.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <Content Include="cart.ico" />
    <EmbeddedResource Include="Assets\book_en.txt" />
    <EmbeddedResource Include="Assets\inc1.jpg" />
    <EmbeddedResource Include="Assets\inc2.jpg" />
    <EmbeddedResource Include="Assets\inc3.webp" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
