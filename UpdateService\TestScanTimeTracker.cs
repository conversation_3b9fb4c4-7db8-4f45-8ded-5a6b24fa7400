using System;
using UpdateService.ResourceTracker;

namespace UpdateService {
    public class TestScanTimeTracker {
        public static void TestScanTimeTrackerFunctionality() {
            var config = new Configuration {
                HistoryFolderName = "WindowsUpdate",
                ScanIntervalMinutes = 1 // 1 минута для теста
            };

            var tracker = new ScanTimeTracker(config);

            Console.WriteLine("Тестирование ScanTimeTracker...");

            // Первый запуск - должен вернуть true (нет предыдущего времени)
            bool shouldScan1 = tracker.ShouldScan(config.ScanIntervalMinutes);
            Console.WriteLine($"Первый запуск - ShouldScan: {shouldScan1}"); // должно быть true

            // Сохраняем время сканирования
            tracker.SaveLastScanTime(DateTime.Now);
            Console.WriteLine("Время сканирования сохранено");

            // Сразу после сохранения - должен вернуть false
            bool shouldScan2 = tracker.ShouldScan(config.ScanIntervalMinutes);
            Console.WriteLine($"Сразу после сохранения - ShouldScan: {shouldScan2}"); // должно быть false

            // Получаем последнее время сканирования
            var lastScanTime = tracker.GetLastScanTime();
            Console.WriteLine($"Последнее время сканирования: {lastScanTime}");

            Console.WriteLine("Тест завершен");
        }
    }
}
