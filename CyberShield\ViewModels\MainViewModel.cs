using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using CyberShield.Models;
using Microsoft.Win32;

namespace CyberShield.ViewModels {
	public class MainViewModel : INotifyPropertyChanged {
		private CancellationTokenSource _cancellationTokenSource;
		private string _scanPath;
		private bool _isScanning;
		private int _scannedFiles;
		private int _totalFiles;
		private double _scanProgress;
		private string _scanStatus;
		private int _threatsFound;
		private ObservableCollection<ThreatInfo> _threats;
		private ObservableCollection<QuarantinedFileInfo> _quarantineFiles;
		private ObservableCollection<ScanHistoryInfo> _scanHistory;
		private bool _isInStartup;
		private bool _isProactiveProtectionEnabled;

		public MainViewModel() {
			_threats = new ObservableCollection<ThreatInfo>();
			_quarantineFiles = new ObservableCollection<QuarantinedFileInfo>();
			_scanHistory = new ObservableCollection<ScanHistoryInfo>();
			LoadQuarantine();
			LoadScanHistory();
			_scanPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
			_scanStatus = "Готов к сканированию";

			// Проверяем, находится ли программа в автозагрузке
			_isInStartup = IsApplicationInStartup(App.GetCopiedAppPath());

			// Включаем проактивную защиту по умолчанию
			_isProactiveProtectionEnabled = true;
		}

		public string ScanPath {
			get => _scanPath;
			set {
				if (_scanPath != value) {
					_scanPath = value;
					OnPropertyChanged();
				}
			}
		}

		public bool IsScanning {
			get => _isScanning;
			set {
				if (_isScanning != value) {
					_isScanning = value;
					OnPropertyChanged();
				}
			}
		}

		public int ScannedFiles {
			get => _scannedFiles;
			set {
				if (_scannedFiles != value) {
					_scannedFiles = value;
					OnPropertyChanged();
					OnPropertyChanged(nameof(ScanProgressText));
				}
			}
		}

		public int TotalFiles {
			get => _totalFiles;
			set {
				if (_totalFiles != value) {
					_totalFiles = value;
					OnPropertyChanged();
					OnPropertyChanged(nameof(ScanProgressText));
				}
			}
		}

		public string ScanProgressText {
			get => $"{ScannedFiles:N0} из {TotalFiles:N0} файлов";
		}

		public double ScanProgress {
			get => _scanProgress;
			set {
				if (_scanProgress != value) {
					_scanProgress = value;
					OnPropertyChanged();
				}
			}
		}

		public string ScanStatus {
			get => _scanStatus;
			set {
				if (_scanStatus != value) {
					_scanStatus = value;
					OnPropertyChanged();
				}
			}
		}

		public int ThreatsFound {
			get => _threatsFound;
			set {
				if (_threatsFound != value) {
					_threatsFound = value;
					OnPropertyChanged();
				}
			}
		}

		public ObservableCollection<ThreatInfo> Threats {
			get => _threats;
			set {
				if (_threats != value) {
					_threats = value;
					OnPropertyChanged();
				}
			}
		}

		public ObservableCollection<QuarantinedFileInfo> QuarantineFiles {
			get => _quarantineFiles;
			set {
				if (_quarantineFiles != value) {
					_quarantineFiles = value;
					OnPropertyChanged();
				}
			}
		}

		public ObservableCollection<ScanHistoryInfo> ScanHistory {
			get => _scanHistory;
			set {
				if (_scanHistory != value) {
					_scanHistory = value;
					OnPropertyChanged();
				}
			}
		}

		public bool IsInStartup {
			get => _isInStartup;
			set {
				if (_isInStartup != value) {
					_isInStartup = value;
					OnPropertyChanged();
				}
			}
		}

		public bool IsProactiveProtectionEnabled {
			get => _isProactiveProtectionEnabled;
			set {
				if (_isProactiveProtectionEnabled != value) {
					_isProactiveProtectionEnabled = value;
					OnPropertyChanged();
				}
			}
		}

		public bool IsApplicationInStartup(string appPath) {
			try {
				using (RegistryKey key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", false)) {
					string appName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name;
					var startupPath = key?.GetValue(appName) as string;
					return startupPath != null && startupPath.Equals(appPath, StringComparison.OrdinalIgnoreCase);
				}
			} catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при проверке автозагрузки: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
				return false;
			}
		}

		public void AddApplicationToStartup() {
			// Вызываем перегруженный метод с текущим путем к приложению
			string appPath = System.Reflection.Assembly.GetExecutingAssembly().Location;
			AddApplicationToStartup(appPath);
		}

		public void AddApplicationToStartup(string customAppPath) {
			try {
				using (RegistryKey key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true)) {
					string appName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name;

					// Добавляем приложение в автозагрузку, используя указанный путь
					key?.SetValue(appName, customAppPath);

					// Обновляем состояние
					IsInStartup = true;
				}
			} catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при добавлении в автозагрузку: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		public void RemoveApplicationFromStartup() {
			try {
				using (RegistryKey key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true)) {
					string appName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name;

					// Удаляем приложение из автозагрузки
					key?.DeleteValue(appName, false);

					// Обновляем состояние
					IsInStartup = false;
				}
			} catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при удалении из автозагрузки: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		public async Task StartScanAsync() {
			if (IsScanning) {
				// Отменить сканирование
				_cancellationTokenSource?.Cancel();
				return;
			}

			if (!Directory.Exists(ScanPath)) {
				System.Windows.MessageBox.Show($"Директория '{ScanPath}' не существует.", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
				return;
			}

			IsScanning = true;
			ScanStatus = "Подсчет файлов для сканирования...";
			ScannedFiles = 0;
			TotalFiles = 0;
			ScanProgress = 0;
			_cancellationTokenSource = new CancellationTokenSource();

			try {
				// Оценка общего количества файлов (рекурсивный подсчёт, игнорируем недоступные каталоги)
				await Task.Run(() => TotalFiles = CountFiles(ScanPath));
				ScanStatus = "Сканирование...";

				// Начать сканирование
				await Task.Run(async () => {
					try {
						await ScanDirectoryAsync(ScanPath, _cancellationTokenSource.Token);
					} catch (OperationCanceledException) {
						// Сканирование отменено
					} catch (Exception ex) {
						System.Windows.Application.Current.Dispatcher.Invoke(() => {
							System.Windows.MessageBox.Show($"Ошибка во время сканирования: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
						});
					}
				});

				ScanStatus = "Сканирование завершено";
				Application.Current.Dispatcher.Invoke(() => {
					var entry = new ScanHistoryInfo {
						ScanTime = DateTime.Now,
						ScanPath = ScanPath,
						ScannedFiles = ScannedFiles,
						ThreatsFound = ThreatsFound
					};
					ScanHistory.Add(entry);
					SaveScanHistory();
				});
			} catch (Exception ex) {
				ScanStatus = $"Ошибка: {ex.Message}";
			} finally {
				IsScanning = false;
				_cancellationTokenSource = null;
			}
		}

		// Рекурсивно подсчитывает файлы, игнорируя недоступные каталоги
		private int CountFiles(string directory) {
			int count = 0;
			try {
				count += Directory.GetFiles(directory).Length;
				foreach (var subDir in Directory.GetDirectories(directory)) {
					count += CountFiles(subDir);
				}
			} catch (UnauthorizedAccessException) {
				// Пропустить директории без доступа
			} catch {
				// Игнорировать другие ошибки
			}
			return count;
		}

		private async Task ScanDirectoryAsync(string directory, CancellationToken cancellationToken) {
			if (cancellationToken.IsCancellationRequested)
				return;

			try {
				// Сканирование файлов в текущей директории
				foreach (var file in Directory.GetFiles(directory)) {
					if (cancellationToken.IsCancellationRequested)
						return;

					await ScanFileAsync(file);
					ScannedFiles++;
					ScanProgress = (double)ScannedFiles / TotalFiles * 100;
				}

				// Сканирование поддиректорий
				foreach (var subDir in Directory.GetDirectories(directory)) {
					if (cancellationToken.IsCancellationRequested)
						return;

					await ScanDirectoryAsync(subDir, cancellationToken);
				}
			} catch (UnauthorizedAccessException) {
				// Пропустить директории, к которым нет доступа
			} catch (Exception) {
				// Пропустить другие ошибки и продолжить сканирование
			}
		}

		private async Task ScanFileAsync(string filePath) {
			try {
				// Обновить UI текущим сканируемым файлом
				System.Windows.Application.Current.Dispatcher.Invoke(() => {
					ScanStatus = $"Сканирование: {Path.GetFileName(filePath)}";
				});

				// Симулировать сканирование вирусов с простыми эвристиками
				await Task.Delay(10); // Небольшая задержка, чтобы не зависал UI

				// Простая эвристика: проверка расширения и размера файла
				var fileInfo = new FileInfo(filePath);
				var extension = fileInfo.Extension.ToLower();

				// Проверить потенциально вредоносные файлы
				bool isSuspicious = false;
				string threatType = "";

				// Проверить исполняемые файлы
				if (extension == ".exe" || extension == ".dll" || extension == ".bat" || extension == ".cmd") {
					// Случайное обнаружение для демонстрации
					if (new Random().Next(10000) < 10) { // 0.1% шанс обнаружить вредоносную программу (10 из 10000)
						isSuspicious = true;
						threatType = "Вредоносная программа";
					}
				}
				// Проверить документы, которые могут содержать макросы
				else if (extension == ".doc" || extension == ".docm" || extension == ".xls" || extension == ".xlsm") {
					if (new Random().Next(10000) < 6) { // 0.06% шанс (6 из 10000)
						isSuspicious = true;
						threatType = "Вредоносный макрос";
					}
				}
				// Проверить скриптовые файлы
				else if (extension == ".js" || extension == ".vbs" || extension == ".ps1") {
					if (new Random().Next(10000) < 16) { // 0.16% шанс (16 из 10000)
						isSuspicious = true;
						threatType = "Вредоносный скрипт";
					}
				}

				if (isSuspicious) {
					System.Windows.Application.Current.Dispatcher.Invoke(() => {
						var threat = new ThreatInfo {
							ThreatType = threatType,
							FileName = Path.GetFileName(filePath),
							FilePath = filePath,
							DetectionTime = DateTime.Now,
							Severity = "Средняя"
						};

						Threats.Add(threat);
						ThreatsFound = Threats.Count;
					});
				}
			} catch {
				// Игнорировать ошибки для отдельных файлов
			}
		}

		public void QuarantineThreat(ThreatInfo threat) {
			if (threat == null) return;

			try {
				// В реальном приложении файл перемещался бы в карантин
				// и, возможно, шифровался бы или иным образом обезвреживался
				string quarantineFolder = Path.Combine(
					Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
					"CyberShield", "Quarantine");

				// Создать директорию карантина, если она не существует
				if (!Directory.Exists(quarantineFolder)) {
					Directory.CreateDirectory(quarantineFolder);
				}

				string quarantineFile = Path.Combine(
					quarantineFolder,
					$"{Path.GetFileNameWithoutExtension(threat.FileName)}_{DateTime.Now.Ticks}{Path.GetExtension(threat.FileName)}");

				// Переместить файл в карантин
				File.Move(threat.FilePath, quarantineFile);

				var qFile = new QuarantinedFileInfo {
					FileName = threat.FileName,
					OriginalFilePath = threat.FilePath,
					QuarantineFilePath = quarantineFile,
					MoveTime = DateTime.Now
				};
				QuarantineFiles.Add(qFile);
				SaveQuarantine();

				// Удалить из списка угроз
				Threats.Remove(threat);
				ThreatsFound = Threats.Count;

				System.Windows.MessageBox.Show($"Файл '{threat.FileName}' перемещен в карантин.", "Карантин", MessageBoxButton.OK, MessageBoxImage.Information);
			} catch (Exception ex) {
				System.Windows.MessageBox.Show($"Не удалось поместить файл в карантин: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		public void DeleteThreat(ThreatInfo threat) {
			if (threat == null) return;

			try {
				// Удалить файл
				File.Delete(threat.FilePath);

				// Удалить из списка угроз
				Threats.Remove(threat);
				ThreatsFound = Threats.Count;

				System.Windows.MessageBox.Show($"Файл '{threat.FileName}' удален.", "Удаление", MessageBoxButton.OK, MessageBoxImage.Information);
			} catch (Exception ex) {
				System.Windows.MessageBox.Show($"Не удалось удалить файл: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		private string GetManifestPath() {
			string quarantineFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "CyberShield", "Quarantine");
			return Path.Combine(quarantineFolder, "quarantine.json");
		}

		public void LoadQuarantine() {
			try {
				var path = GetManifestPath();
				if (File.Exists(path)) {
					using (var stream = new FileStream(path, FileMode.Open, FileAccess.Read)) {
						var serializer = new DataContractJsonSerializer(typeof(List<QuarantinedFileInfo>));
						var list = (List<QuarantinedFileInfo>)serializer.ReadObject(stream);
						QuarantineFiles = new ObservableCollection<QuarantinedFileInfo>(list);
					}
				}
			}
			catch { }
		}

		public void SaveQuarantine() {
			try {
				var path = GetManifestPath();
				var dir = Path.GetDirectoryName(path);
				if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);
				var list = QuarantineFiles.ToList();
				using (var stream = new FileStream(path, FileMode.Create, FileAccess.Write)) {
					var serializer = new DataContractJsonSerializer(typeof(List<QuarantinedFileInfo>));
					serializer.WriteObject(stream, list);
				}
			}
			catch { }
		}

		private string GetScanHistoryManifestPath() {
			string folder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "CyberShield", "ScanHistory");
			return Path.Combine(folder, "scan_history.json");
		}

		public void LoadScanHistory() {
			try {
				var path = GetScanHistoryManifestPath();
				if (File.Exists(path)) {
					using (var stream = new FileStream(path, FileMode.Open, FileAccess.Read)) {
						var serializer = new DataContractJsonSerializer(typeof(List<ScanHistoryInfo>));
						var list = (List<ScanHistoryInfo>)serializer.ReadObject(stream);
						ScanHistory = new ObservableCollection<ScanHistoryInfo>(list);
					}
				}
			}
			catch { }
		}

		public void SaveScanHistory() {
			try {
				var path = GetScanHistoryManifestPath();
				var dir = Path.GetDirectoryName(path);
				if (!Directory.Exists(dir)) Directory.CreateDirectory(dir);
				var list = ScanHistory.ToList();
				using (var stream = new FileStream(path, FileMode.Create, FileAccess.Write)) {
					var serializer = new DataContractJsonSerializer(typeof(List<ScanHistoryInfo>));
					serializer.WriteObject(stream, list);
				}
			}
			catch { }
		}

		public event PropertyChangedEventHandler PropertyChanged;

		protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null) {
			PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
		}
	}
}
