using System;
using System.Globalization;
using System.Windows.Data;

namespace CyberShield.Converters {
	public class BoolToScanTextConverter : IValueConverter {
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture) {
			if (value is bool isScanning) {
				return isScanning ? "Остановить" : "Сканировать";
			}
			return "Сканировать";
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
			throw new NotImplementedException();
		}
	}
}
