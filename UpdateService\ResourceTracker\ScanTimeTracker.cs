using System;
using System.IO;

namespace UpdateService.ResourceTracker {
	public class ScanTimeTracker {
		private readonly string _scanTimeFilePath;

		public ScanTimeTracker(Configuration config) {
			string historyFolder = Path.Combine(
				Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
				config.HistoryFolderName
			);

			if (!Directory.Exists(historyFolder)) {
				Directory.CreateDirectory(historyFolder);
			}

			_scanTimeFilePath = Path.Combine(historyFolder, "last_scan_time.dat");
		}

		public DateTime? GetLastScanTime() {
			try {
				if (File.Exists(_scanTimeFilePath)) {
					string timeString = File.ReadAllText(_scanTimeFilePath);
					if (DateTime.TryParse(timeString, out DateTime lastScanTime)) {
						return lastScanTime;
					}
				}
			} catch {
				// игнорируем ошибки при чтении
			}
			return null;
		}

		public void SaveLastScanTime(DateTime scanTime) {
			try {
				File.WriteAllText(_scanTimeFilePath, scanTime.ToString("O"));
			} catch {
				// игнорируем ошибки при сохранении
			}
		}

		public bool ShouldScan(int intervalMinutes) {
			var lastScanTime = GetLastScanTime();
			if (lastScanTime == null) {
				return true; // если время последнего сканирования неизвестно, сканируем
			}

			var timeSinceLastScan = DateTime.Now - lastScanTime.Value;
			return timeSinceLastScan.TotalMinutes >= intervalMinutes;
		}
	}
}
