﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35013.160
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CyberGun", "CyberGun\CyberGun.csproj", "{9763D24C-294B-4A2F-B833-055638069553}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fp_Vuz", "Fp_Vuz\Fp_Vuz.csproj", "{9763D24C-294B-4A2F-B833-055638069554}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Joiner", "Jo<PERSON>\Joiner.csproj", "{8895EB7F-1725-4236-A50F-961330028D3B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YodasSweeper", "DuplicateFinder\YodasSweeper.csproj", "{5CFC748C-97DD-4D5C-A45F-F093EAEFCC20}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CyberShield", "CyberShield\CyberShield.csproj", "{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UpdateService", "UpdateService\UpdateService.csproj", "{B2C3D4E5-F6A7-8901-2345-678901BCDEFG}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9763D24C-294B-4A2F-B833-055638069553}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9763D24C-294B-4A2F-B833-055638069553}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9763D24C-294B-4A2F-B833-055638069553}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9763D24C-294B-4A2F-B833-055638069553}.Release|Any CPU.Build.0 = Release|Any CPU
		{9763D24C-294B-4A2F-B833-055638069554}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9763D24C-294B-4A2F-B833-055638069554}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9763D24C-294B-4A2F-B833-055638069554}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9763D24C-294B-4A2F-B833-055638069554}.Release|Any CPU.Build.0 = Release|Any CPU
		{8895EB7F-1725-4236-A50F-961330028D3B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8895EB7F-1725-4236-A50F-961330028D3B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8895EB7F-1725-4236-A50F-961330028D3B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8895EB7F-1725-4236-A50F-961330028D3B}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CFC748C-97DD-4D5C-A45F-F093EAEFCC20}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CFC748C-97DD-4D5C-A45F-F093EAEFCC20}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CFC748C-97DD-4D5C-A45F-F093EAEFCC20}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CFC748C-97DD-4D5C-A45F-F093EAEFCC20}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6A7-8901-2345-678901BCDEFG}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6A7-8901-2345-678901BCDEFG}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6A7-8901-2345-678901BCDEFG}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6A7-8901-2345-678901BCDEFG}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D0062FD2-A77D-44AF-8E98-A93F54959786}
	EndGlobalSection
EndGlobal
