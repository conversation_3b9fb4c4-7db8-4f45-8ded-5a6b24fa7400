using System.Collections.Generic;
using System.IO;

namespace Fp_Vuz.ResourceTracker {
	public class FileComparer : IEqualityComparer<string> {
		public bool Equals(string x, string y) {
			if (x == y) return true;
			if (x == null || y == null) return false;

			try {
				var fileX = new FileInfo(x);
				var fileY = new FileInfo(y);

				return fileX.Name == fileY.Name &&
					   fileX.Length == fileY.Length &&
					   fileX.LastWriteTime == fileY.LastWriteTime;
			} catch {
				return false;
			}
		}

		public int GetHashCode(string filePath) {
			try {
				var file = new FileInfo(filePath);
				unchecked {
					int hash = 17;
					hash = hash * 23 + (file.Name?.GetHashCode() ?? 0);
					hash = hash * 23 + file.LastWriteTime.GetHashCode();
					hash = hash * 23 + file.Length.GetHashCode();
					return hash;
				}
			} catch {
				return filePath?.GetHashCode() ?? 0;
			}
		}
	}
}
