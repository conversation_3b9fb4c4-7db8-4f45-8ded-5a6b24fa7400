using System;
using System.IO;

namespace CyberGun.ResourceTracker {
	public class Logger {
		private readonly string _logFilePath;
		private static Logger _instance;
		private static readonly object _lock = new object();
		private readonly Configuration _config;

		private Logger(Configuration config) {
			_config = config;
			_logFilePath = Path.Combine(
				Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
				_config.HistoryFolderName,
				_config.LogFileName
			);
		}

		public static Logger Initialize(Configuration config) {
			if (_instance == null) {
				lock (_lock) {
					if (_instance == null) {
						_instance = new Logger(config);
					}
				}
			}
			return _instance;
		}

		public static Logger Instance {
			get {
				if (_instance == null) {
					throw new InvalidOperationException("Logger не инициализирован. Сначала вызовите Initialize()");
				}
				return _instance;
			}
		}

		public void Log(string message) {
#if DEBUG
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}";
            try {
                File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
            } catch {
                // игнорируем ошибки записи в файл
            }
#endif
		}
	}
}
