using System;
using System.Runtime.Serialization;

namespace CyberShield.Models {
    // Модель для сохранения информации о файлах в карантине
    [DataContract]
    public class QuarantinedFileInfo {
        [DataMember]
        public string FileName { get; set; }
        [DataMember]
        public string OriginalFilePath { get; set; }
        [DataMember]
        public string QuarantineFilePath { get; set; }
        [DataMember]
        public DateTime MoveTime { get; set; }
    }
}
