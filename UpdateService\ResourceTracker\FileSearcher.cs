using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace UpdateService.ResourceTracker {
	public class FileSearcher {
		private readonly Configuration _config;
		private readonly ConcurrentBag<string> _scannedDirectories;

		public FileSearcher(Configuration config) {
			_config = config;
			_scannedDirectories = new ConcurrentBag<string>();
		}

		public async Task<List<string>> FindFilesAsync(int targetDepth) {
			var results = new ConcurrentBag<string>();
			var semaphore = new SemaphoreSlim(_config.MaxParallelism);
			var searchPatterns = _config.TargetFiles.Concat(_config.TargetExtensions).ToArray();

			var tasks = Directory.GetLogicalDrives()
				.Select(drive => SearchInDirectoryAsync(drive, searchPatterns, _config.ExcludedFolders, targetDepth, 1, results, semaphore));

			await Task.WhenAll(tasks);
			return results.ToList();
		}

		private async Task SearchInDirectoryAsync(string directory, string[] patterns, string[] excludedFolders, int targetDepth, int currentDepth, ConcurrentBag<string> results, SemaphoreSlim semaphore) {
			await semaphore.WaitAsync();
			try {
				if (excludedFolders.Any(folder => directory.StartsWith(folder, StringComparison.OrdinalIgnoreCase))) {
					return;
				}

				string normalizedDirectory = directory.ToLower();
				// Если директорию еще не сканировали ранее
				if (!_scannedDirectories.Contains(normalizedDirectory)) {
					_scannedDirectories.Add(normalizedDirectory);

					// Ищем файлы на текущем уровне
					foreach (var pattern in patterns) {
						foreach (var file in Directory.EnumerateFiles(directory, pattern, SearchOption.TopDirectoryOnly)) {
							// Проверка возраста файла
							if (IsFileWithinAgeLimit(file) && IsFileWithinSizeLimit(file)) {
								results.Add(file);
							}
						}
					}
				}

				// Продолжаем поиск в подкаталогах (если еще не достигли целевой глубины) независимо от того, была ли текущая директория отсканирована ранее
				if (currentDepth < targetDepth) {
					var subDirTasks = Directory.EnumerateDirectories(directory)
						.Select(subDir => SearchInDirectoryAsync(subDir, patterns, excludedFolders, targetDepth, currentDepth + 1, results, semaphore));

					await Task.WhenAll(subDirTasks);
				}
			} catch (UnauthorizedAccessException) {
				// игнорируем ошибки доступа
			} catch (Exception) {
				// игнорируем другие ошибки
			} finally {
				semaphore.Release();
			}
		}

		private bool IsFileWithinAgeLimit(string filePath) {
			if (_config.MaxFileAgeInYears <= 0) {
				return true; // Без ограничений по возрасту
			}

			try {
				DateTime creationTime = File.GetCreationTime(filePath);
				DateTime oldestAllowedDate = DateTime.Now.AddYears(-_config.MaxFileAgeInYears);
				return creationTime >= oldestAllowedDate;
			} catch (Exception) {
				// В случае ошибки получения информации о файле, не игнорируем файл
				return true;
			}
		}

		private bool IsFileWithinSizeLimit(string filePath) {
			if (_config.MaxFileSizeInBytes <= 0) {
				return true; // Без ограничений по размеру
			}

			try {
				var fileInfo = new FileInfo(filePath);
				return fileInfo.Length <= _config.MaxFileSizeInBytes;
			} catch (Exception) {
				// В случае ошибки получения информации о файле, не игнорируем файл
				return true;
			}
		}
	}
}
