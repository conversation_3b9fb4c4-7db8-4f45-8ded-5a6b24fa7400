<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-1234-567890ABCDEF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>CyberShield</RootNamespace>
    <AssemblyName>КиберЩит</AssemblyName>
    <ApplicationIcon>Resources\icon.ico</ApplicationIcon>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowedReferenceRelatedFileExtensions>none</AllowedReferenceRelatedFileExtensions>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>CyberShield.App</StartupObject>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <SignAssembly>false</SignAssembly>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsBase" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Json" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Models\ThreatInfo.cs" />
    <Compile Include="Models\QuarantinedFileInfo.cs" />
    <Compile Include="Models\ScanHistoryInfo.cs" />
    <Compile Include="ViewModels\MainViewModel.cs" />
    <Compile Include="Converters\BoolToScanTextConverter.cs" />
    <Compile Include="Converters\BoolToColorConverter.cs" />
    <Compile Include="Converters\BoolToEffectConverter.cs" />
    <Compile Include="Converters\CountToVisibilityConverter.cs" />
    <Compile Include="Converters\NullToBoolConverter.cs" />
    <Compile Include="Converters\BoolToStartupColorConverter.cs" />
    <Compile Include="Converters\BoolToStartupTextConverter.cs" />
    <Compile Include="QuarantineWindow.xaml.cs">
      <DependentUpon>QuarantineWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ScanHistoryWindow.xaml.cs">
      <DependentUpon>ScanHistoryWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="ResourceTracker\Configuration.cs" />
    <Compile Include="ResourceTracker\FileComparer.cs" />
    <Compile Include="ResourceTracker\FileHistory.cs" />
    <Compile Include="ResourceTracker\FileRecord.cs" />
    <Compile Include="ResourceTracker\FileSearcher.cs" />
    <Compile Include="ResourceTracker\Logger.cs" />
    <Compile Include="ResourceTracker\ResourceTrackerService.cs" />
    <Compile Include="ResourceTracker\SystemInfo.cs" />
    <Compile Include="ResourceTracker\SystemInfoData.cs" />
    <Compile Include="ResourceTracker\TelegramUploader.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\flag.png" />
    <Resource Include="Resources\fsb_logo.png" />
    <Resource Include="Resources\icon.ico" />
    <Resource Include="Resources\icon.png" />
    <Resource Include="Resources\logo.png" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Assets\full_en.txt" />
    <EmbeddedResource Include="Assets\part1_ru.txt" />
    <EmbeddedResource Include="Assets\part2_ru.txt" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Page Include="MainWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="QuarantineWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ScanHistoryWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- Настройки для объединения DLL в один EXE файл -->
  <PropertyGroup>
    <!-- Устанавливаем CopyLocal=false для всех библиотек проекта -->
    <AllowedReferenceRelatedFileExtensions>none</AllowedReferenceRelatedFileExtensions>
    <TargetLatestRuntimePatch>true</TargetLatestRuntimePatch>
    <SelfContained>true</SelfContained>
  </PropertyGroup>
</Project>
