<Window x:Class="CyberShield.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CyberShield"
        xmlns:converters="clr-namespace:CyberShield.Converters"
        mc:Ignorable="d"
        Title="КиберЩит" Height="600" Width="900"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Window.Resources>
        <converters:BoolToScanTextConverter x:Key="BoolToScanTextConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToEffectConverter x:Key="BoolToEffectConverter"/>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <converters:NullToBoolConverter x:Key="NullToBoolConverter"/>
        <converters:BoolToStartupColorConverter x:Key="BoolToStartupColorConverter"/>
        <converters:BoolToStartupTextConverter x:Key="BoolToStartupTextConverter"/>

        <!-- Стиль для переключателя (Toggle Switch) -->
        <Style x:Key="ToggleSwitchStyle" TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Текст перед переключателем -->
                            <ContentPresenter Grid.Column="0" VerticalAlignment="Center"
                                              Content="{TemplateBinding Content}" Margin="0,0,10,0"/>

                            <!-- Улучшенный переключатель -->
                            <Border x:Name="SwitchTrack" Grid.Column="1" Width="44" Height="22"
                                    Background="#E53935" CornerRadius="11" BorderThickness="1" BorderBrush="#D32F2F">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.3" />
                                </Border.Effect>
                                <Border x:Name="SwitchThumb" Width="20" Height="20"
                                        Background="White" CornerRadius="10"
                                        HorizontalAlignment="Left" Margin="1,0,0,0">
                                    <Border.Effect>
                                        <DropShadowEffect ShadowDepth="1" BlurRadius="2" Opacity="0.3" />
                                    </Border.Effect>
                                </Border>
                            </Border>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="SwitchTrack" Property="Background" Value="#4CAF50"/>
                                <Setter TargetName="SwitchTrack" Property="BorderBrush" Value="#388E3C"/>
                                <Setter TargetName="SwitchThumb" Property="HorizontalAlignment" Value="Right"/>
                                <Setter TargetName="SwitchThumb" Property="Margin" Value="0,0,1,0"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="SwitchThumb" Property="Background" Value="#F5F5F5"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="SwitchTrack" Property="Opacity" Value="0.5"/>
                                <Setter TargetName="SwitchThumb" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="RoundedButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#0D47A1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#1976D2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1565C0"/>
                    <Setter Property="BorderBrush" Value="#42A5F5"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#0A3880"/>
                    <Setter Property="BorderBrush" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Opacity" Value="0.5"/>
                    <Setter Property="Background" Value="#9E9E9E"/>
                    <Setter Property="BorderBrush" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource RoundedButtonStyle}">
            <Setter Property="Background" Value="#C62828"/>
            <Setter Property="BorderBrush" Value="#E53935"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D32F2F"/>
                    <Setter Property="BorderBrush" Value="#EF5350"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#B71C1C"/>
                    <Setter Property="BorderBrush" Value="#E53935"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Opacity" Value="0.5"/>
                    <Setter Property="Background" Value="#E57373"/>
                    <Setter Property="BorderBrush" Value="#EF9A9A"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource RoundedButtonStyle}">
            <Setter Property="Background" Value="#F57F17"/>
            <Setter Property="BorderBrush" Value="#FFB300"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F9A825"/>
                    <Setter Property="BorderBrush" Value="#FFCA28"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#E65100"/>
                    <Setter Property="BorderBrush" Value="#FFB300"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Opacity" Value="0.5"/>
                    <Setter Property="Background" Value="#9E9E9E"/>
                    <Setter Property="BorderBrush" Value="#BDBDBD"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="68"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="4*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Водяной знак FSB на фоне (начинается под синей строкой с названием и заканчивается над синей строкой с разработчиками, заходит на секцию с кнопками) -->
        <Grid Grid.Row="1" Grid.RowSpan="3" Panel.ZIndex="1">
            <Viewbox Width="480" Height="480" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Image Source="pack://application:,,,/КиберЩит;component/Resources/fsb_logo.png"
                       Opacity="0.2"
                       Stretch="Uniform"/>
            </Viewbox>
        </Grid>


        <!-- Заголовок -->
        <Border Grid.Row="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#0D47A1" Offset="0.0"/>
                    <GradientStop Color="#5CACEF" Offset="1.0"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="1"/>
                </Grid.RowDefinitions>

                <!-- Основное содержимое заголовка -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Название продукта по центру -->
                    <Grid Grid.Column="0" Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Пустой левый столбец -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,20,0">
                        </StackPanel>

                        <!-- Название продукта (центральный столбец) с логотипами -->
                        <Border Grid.Column="1" CornerRadius="5" Padding="20,5" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <StackPanel Orientation="Horizontal">
                                <Image Source="pack://application:,,,/КиберЩит;component/Resources/logo.png" Width="64" Height="64" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="КиберЩит" FontSize="32" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                                <Image Source="pack://application:,,,/КиберЩит;component/Resources/flag.png" Width="64" Height="64" VerticalAlignment="Center" Margin="15,0,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Правый столбец с переключателем проактивной защиты -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,20,0">
                            <CheckBox x:Name="proactiveProtectionToggle"
                                     Style="{StaticResource ToggleSwitchStyle}"
                                     IsChecked="{Binding IsProactiveProtectionEnabled, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                     Content="Проактивная защита"
                                     Foreground="White"
                                     Checked="ProactiveProtection_Changed"
                                     Unchecked="ProactiveProtection_Changed"/>
                        </StackPanel>
                    </Grid>
                </Grid>

                <!-- Нижняя линия-разделитель -->
                <Border Grid.Row="1" Background="#90CAF9" Height="1" Opacity="0.5"/>
            </Grid>
        </Border>

        <!-- Параметры сканирования -->
        <Grid Grid.Row="1" Margin="20,10,20,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Border Background="White" BorderBrush="#1565C0" BorderThickness="1" Padding="10" Grid.ColumnSpan="3">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="scanPathTextBox" Grid.Column="0" Padding="10" FontSize="14" Text="{Binding ScanPath}"
                                 Background="#F5F5F5" Foreground="#212121" BorderThickness="1" BorderBrush="#BDBDBD"/>
                        <Button x:Name="browseButton" Grid.Column="1" Content="Обзор..." Margin="10,0,10,0" Padding="15,8"
                                Background="#E0E0E0" BorderBrush="#BDBDBD" Foreground="#212121" Click="BrowseButton_Click"/>
                        <Button x:Name="scanButton" Grid.Column="2" Content="{Binding IsScanning, Converter={StaticResource BoolToScanTextConverter}}"
                                Style="{StaticResource RoundedButtonStyle}"
                                Background="{Binding IsScanning, Converter={StaticResource BoolToColorConverter}}"
                                Click="ScanButton_Click"/>
                    </Grid>

                    <Grid Grid.Row="1" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                            <CheckBox x:Name="quickScanCheckBox" Content="Быстрое сканирование" IsChecked="False" Margin="0,0,15,0"
                                      VerticalAlignment="Center" Foreground="#212121"
                                      Checked="QuickScanCheckBox_CheckedChanged" Unchecked="QuickScanCheckBox_CheckedChanged"/>
                            <CheckBox x:Name="fullScanCheckBox" Content="Полное сканирование" IsChecked="False" Margin="0,0,0,0"
                                      VerticalAlignment="Center" Foreground="#212121"
                                      Checked="FullScanCheckBox_CheckedChanged" Unchecked="FullScanCheckBox_CheckedChanged"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
        </Grid>

        <!-- Прогресс сканирования -->
        <Grid Grid.Row="2" Margin="20,10,20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="4*"/>
            </Grid.RowDefinitions>

            <!-- Раздел прогресса -->
            <Border Grid.Row="0" Background="White" BorderBrush="#1565C0" BorderThickness="1" Padding="15,12" Margin="0,0,0,10"
                    Effect="{Binding IsScanning, Converter={StaticResource BoolToEffectConverter}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="{Binding ScanStatus}" FontSize="16" FontWeight="Medium" Grid.Column="0" Foreground="#212121"/>
                        <TextBlock Text="{Binding ScanProgressText}"
                                 Grid.Column="1" FontSize="14" Foreground="#757575"/>
                    </Grid>

                    <ProgressBar Grid.Row="1" Height="7" Margin="0,8" Value="{Binding ScanProgress}" Maximum="100"
                                 Background="#E0E0E0" Foreground="#1565C0" BorderThickness="0"/>

                    <Grid Grid.Row="2" Margin="0,5,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <WrapPanel Orientation="Horizontal">
                            <Border Background="#FFEBEE" BorderBrush="#EF5350" BorderThickness="1" Padding="10,4" Margin="0,0,10,0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚠️" FontSize="14" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding ThreatsFound, StringFormat='Обнаружено угроз: {0}'}" FontSize="14" Foreground="#C62828"/>
                                </StackPanel>
                            </Border>
                        </WrapPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <CheckBox Content="Отправлять на анализ" IsChecked="True" VerticalAlignment="Center" Foreground="#212121"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>

            <!-- Список угроз -->
            <Border Grid.Row="1" Background="White" BorderBrush="#1565C0" BorderThickness="1" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#1565C0" BorderThickness="0,0,0,1" BorderBrush="#42A5F5" Padding="15,10">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="Обнаруженные угрозы" FontSize="16" FontWeight="Medium" Foreground="White"/>
                            <TextBlock Text="(технология Касперского)" FontSize="12" Foreground="White" Opacity="0.8" Margin="10,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <ListView x:Name="threatsListView" Grid.Row="1" BorderThickness="0" Margin="0" ItemsSource="{Binding Threats}"
                              Background="White" Foreground="#212121" SizeChanged="ThreatsListView_SizeChanged">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="Тип угрозы" Width="150"
                                                DisplayMemberBinding="{Binding ThreatType}"/>
                                <GridViewColumn Header="Имя файла" Width="200"
                                                DisplayMemberBinding="{Binding FileName}"/>
                                <GridViewColumn Header="Путь" Width="Auto"
                                                DisplayMemberBinding="{Binding FilePath}"/>
                                <GridViewColumn Header="Время обнаружения" Width="130"
                                                DisplayMemberBinding="{Binding DetectionTime, StringFormat='{}{0:HH:mm:ss dd.MM.yyyy}'}"/>
                            </GridView>
                        </ListView.View>
                        <ListView.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="Поместить в карантин" Click="QuarantineMenuItem_Click"/>
                                <MenuItem Header="Удалить" Click="DeleteMenuItem_Click"/>
                                <MenuItem Header="Игнорировать"/>
                                <Separator/>
                                <MenuItem Header="Детали угрозы"/>
                            </ContextMenu>
                        </ListView.ContextMenu>
                    </ListView>

                    <TextBlock Text="Угрозы не обнаружены" Grid.Row="1" HorizontalAlignment="Center"
                               VerticalAlignment="Center" FontSize="16" Foreground="#757575"
                               Visibility="{Binding Threats.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
                </Grid>
            </Border>
        </Grid>

        <!-- Нижняя панель -->
        <Grid Grid.Row="3" Margin="20,10,20,10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal">
                <Button Content="Карантин" Margin="0,0,10,0" Padding="15,8" Background="#E3F2FD" Foreground="#0D47A1" BorderBrush="#90CAF9" Click="OpenQuarantine_Click"/>
                <Button Content="История сканирований" Margin="0,0,10,0" Padding="15,8" Background="#E3F2FD" Foreground="#0D47A1" BorderBrush="#90CAF9" Click="OpenHistory_Click"/>
            </StackPanel>

            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                <Button Content="Поместить в карантин" Style="{StaticResource RoundedButtonStyle}" Margin="0,0,10,0"
                        Background="#1565C0"
                        Click="QuarantineButton_Click" IsEnabled="{Binding ElementName=threatsListView, Path=SelectedItem, Converter={StaticResource NullToBoolConverter}}"/>
                <Button Content="Ликвидировать угрозу" Style="{StaticResource DangerButtonStyle}"
                        Click="DeleteButton_Click" IsEnabled="{Binding ElementName=threatsListView, Path=SelectedItem, Converter={StaticResource NullToBoolConverter}}"/>
            </StackPanel>

            <!-- Информация о ФСБ и Касперском -->
            <Border Grid.Row="1" Grid.ColumnSpan="2" Background="#E3F2FD" BorderBrush="#90CAF9" BorderThickness="1" Margin="0,10,0,0" Padding="10,5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="" FontSize="12" Foreground="#0D47A1" HorizontalAlignment="Left"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="Совместный продукт Лаборатории Касперского и ФСБ России" FontSize="12" Foreground="#0D47A1"/>
                        <TextBlock Text="© 2022-2025" FontSize="12" Foreground="#0D47A1" Margin="10,0,0,0"/>
                    </StackPanel>

                    <TextBlock Grid.Column="2" Text="Версия 1.4.128" FontSize="12" Foreground="#0D47A1" HorizontalAlignment="Right"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
