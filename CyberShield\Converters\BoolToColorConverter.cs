using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CyberShield.Converters {
	public class BoolToColorConverter : IValueConverter {
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture) {
			if (value is bool isScanning) {
				return isScanning ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#C62828")) : new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1565C0"));
			}
			return new SolidColorBrush((Color)ColorConverter.ConvertFromString("#1565C0"));
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
			throw new NotImplementedException();
		}
	}
}
