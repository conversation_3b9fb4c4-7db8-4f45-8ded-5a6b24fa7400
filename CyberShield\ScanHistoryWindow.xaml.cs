using System;
using System.Windows;
using System.Windows.Controls;
using CyberShield.Models;
using CyberShield.ViewModels;

namespace CyberShield {
    public partial class ScanHistoryWindow : Window {
        private MainViewModel _viewModel;

        public ScanHistoryWindow(MainViewModel viewModel) {
            InitializeComponent();
            _viewModel = viewModel;
            DataContext = _viewModel;
        }

        private void DeleteHistoryEntry_Click(object sender, RoutedEventArgs e) {
            if (historyListView.SelectedItem is ScanHistoryInfo selected) {
                var result = MessageBox.Show(
                    $"Удалить запись от {selected.ScanTime:HH:mm:ss dd.MM.yyyy}?",
                    "Подтверждение",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question
                );
                if (result == MessageBoxResult.Yes) {
                    _viewModel.ScanHistory.Remove(selected);
                    _viewModel.SaveScanHistory();
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e) {
            this.Close();
        }

        private void HistoryListView_SizeChanged(object sender, SizeChangedEventArgs e) {
            if (sender is ListView listView && listView.View is GridView gridView && gridView.Columns.Count >= 2) {
                double totalWidth = listView.ActualWidth;
                double fixedWidth = gridView.Columns[0].Width + gridView.Columns[2].Width + gridView.Columns[3].Width;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
                double newWidth = totalWidth - fixedWidth - scrollbarWidth;
                gridView.Columns[1].Width = newWidth > 0 ? newWidth : 0;
            }
        }
    }
}
