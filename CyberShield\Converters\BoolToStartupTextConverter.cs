using System;
using System.Globalization;
using System.Windows.Data;

namespace CyberShield.Converters {
	public class BoolToStartupTextConverter : IValueConverter {
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture) {
			if (value is bool isInStartup) {
				// Возвращаем текст в зависимости от состояния автозагрузки
				return isInStartup ? "Автозагрузка: Включена" : "Автозагрузка: Выключена";
			}
			return "Автозагрузка: Выключена"; // По умолчанию выключена
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
			throw new NotImplementedException();
		}
	}
}
