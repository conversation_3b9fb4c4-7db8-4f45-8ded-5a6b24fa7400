﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;

namespace YodasSweeper {
	internal class Program {
		static void Main(string[] args) {
			Console.OutputEncoding = System.Text.Encoding.UTF8;
			Console.Title = "<PERSON><PERSON>'s Sweeper: Doubles Be Gone";
			string sourceDirectory;

			// Перевірка аргументів командного рядка або інтерактивне введення
			if (args.Length >= 1) {
				sourceDirectory = args[0];
			} else {
				Console.Write("Введіть шлях до директорії для пошуку дублікатів: ");
				sourceDirectory = Console.ReadLine();
			}

			// Перевірка введеного шляху
			if (string.IsNullOrWhiteSpace(sourceDirectory)) {
				Console.WriteLine("Некоректний шлях. Завершення програми.");
				return;
			}

			// Розгортання відносного шляху до повного
			sourceDirectory = Path.GetFullPath(sourceDirectory);

			if (!Directory.Exists(sourceDirectory)) {
				Console.WriteLine($"Директорія {sourceDirectory} не існує.");
				return;
			}

			string duplicatesDirectory = Path.Combine(sourceDirectory, "Duplicates");

			// Створення директорії для дублікатів, якщо її не існує
			Directory.CreateDirectory(duplicatesDirectory);

			// Знайти всі файли у директорії та підпапках
			string[] allFiles = Directory.GetFiles(sourceDirectory, "*.*", SearchOption.AllDirectories);

			// Словник для зберігання хешів файлів
			Dictionary<string, List<string>> fileHashes = new Dictionary<string, List<string>>();

			// Підрахунок кількості оброблених та переміщених файлів
			int processedFiles = 0;
			int movedDuplicates = 0;

			DrawYoda();
			Console.WriteLine($"Пошук дублікатів у директорії: {sourceDirectory}");

			// Обчислення хешів та групування файлів
			foreach (string filePath in allFiles) {
				try {
					processedFiles++;
					string fileHash = ComputeFileHash(filePath);

					if (!fileHashes.ContainsKey(fileHash)) {
						fileHashes[fileHash] = new List<string>();
					}

					fileHashes[fileHash].Add(filePath);

					// Показуємо прогрес кожні 100 файлів
					if (processedFiles % 100 == 0) {
						Console.Write($"\rОброблено файлів: {processedFiles}");
					}
				} catch (Exception ex) {
					Console.WriteLine($"\nПомилка обробки файлу {filePath}: {ex.Message}");
				}
			}

			Console.WriteLine(); // Новий рядок після прогресу

			// Переміщення дублікатів
			foreach (var hashGroup in fileHashes.Where(g => g.Value.Count > 1)) {
				// залишаєм файл з найкоротшою назвою в групі дублікатів, решту переміщуємо
				string masterFile = hashGroup.Value.OrderBy(filePath => Path.GetFileName(filePath).Length).First();
				foreach (string duplicateFile in hashGroup.Value.Where(f => f != masterFile)) {
					try {
						string fileName = Path.GetFileName(duplicateFile);
						string destinationPath = Path.Combine(duplicatesDirectory, fileName);

						// Додаємо унікальний суфікс, якщо файл з таким іменем вже існує
						string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
						string extension = Path.GetExtension(fileName);
						int counter = 1;

						while (File.Exists(destinationPath)) {
							destinationPath = Path.Combine(duplicatesDirectory, $"{fileNameWithoutExtension}_{counter}{extension}");
							counter++;
						}

						File.Move(duplicateFile, destinationPath);
						movedDuplicates++;
					} catch (Exception ex) {
						Console.WriteLine($"Помилка переміщення файлу {duplicateFile}: {ex.Message}");
					}
				}
			}

			// Виведення статистики
			Console.WriteLine($"Загалом перевірено файлів: {processedFiles}");
			Console.WriteLine($"Знайдено файлів що мають дублікати: {fileHashes.Count(g => g.Value.Count > 1)}");
			Console.WriteLine($"Переміщено дублікатів: {movedDuplicates}");
			Console.WriteLine($"Дублікати переміщені до: {duplicatesDirectory}");

			// Очікування натискання клавіші перед завершенням
			Console.WriteLine("\nНатисніть будь-яку клавішу для завершення...");
			Console.ReadKey();
		}

		// Метод для обчислення хешу файлу
		static string ComputeFileHash(string filePath) {
			using (var md5 = MD5.Create()) {
				using (var stream = File.OpenRead(filePath)) {
					var hash = md5.ComputeHash(stream);
					return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
				}
			}
		}

		static void DrawYoda() {
			string[] yodaArt = {
			"⠀⢀⣠⣄⣀⣀⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣀⣤⣴⣶⡾⠿⠿⠿⠿⢷⣶⣦⣤⣀⡀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⢰⣿⡟⠛⠛⠛⠻⠿⠿⢿⣶⣶⣦⣤⣤⣀⣀⡀⣀⣴⣾⡿⠟⠋⠉⠀⠀⠀⠀⠀⠀⠀⠀⠉⠙⠻⢿⣷⣦⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⣀⣀⣀⣀⣀⣀⣀⡀",
			"⠀⠻⣿⣦⡀⠀⠉⠓⠶⢦⣄⣀⠉⠉⠛⠛⠻⠿⠟⠋⠁⠀⠀⠀⣤⡀⠀⠀⢠⠀⠀⠀⣠⠀⠀⠀⠀⠈⠙⠻⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠟⠛⠛⢻⣿",
			"⠀⠀⠈⠻⣿⣦⠀⠀⠀⠀⠈⠙⠻⢷⣶⣤⡀⠀⠀⠀⠀⢀⣀⡀⠀⠙⢷⡀⠸⡇⠀⣰⠇⠀⢀⣀⣀⠀⠀⠀⠀⠀⠀⣀⣠⣤⣤⣶⡶⠶⠶⠒⠂⠀⠀⣠⣾⠟   Магію свою я використовую,",
			"⠀⠀⠀⠀⠈⢿⣷⡀⠀⠀⠀⠀⠀⠀⠈⢻⣿⡄⣠⣴⣿⣯⣭⣽⣷⣆⠀⠁⠀⠀⠀⠀⢠⣾⣿⣿⣿⣿⣦⡀⠀⣠⣾⠟⠋⠁⠀⠀⠀⠀⠀⠀⠀⣠⣾⡟⠁⠀   щоб теку твою від сміття москальського очистити.",
			"⠀⠀⠀⠀⠀⠈⢻⣷⣄⠀⠀⠀⠀⠀⠀⠀⣿⡗⢻⣿⣧⣽⣿⣿⣿⣧⠀⠀⣀⣀⠀⢠⣿⣧⣼⣿⣿⣿⣿⠗⠰⣿⠃⠀⠀⠀⠀⠀⠀⠀⠀⣠⣾⡿⠋⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠙⢿⣶⣄⡀⠀⠀⠀⠀⠸⠃⠈⠻⣿⣿⣿⣿⣿⡿⠃⠾⣥⡬⠗⠸⣿⣿⣿⣿⣿⡿⠛⠀⢀⡟⠀⠀⠀⠀⠀⠀⣀⣠⣾⡿⠋⠀⠀⠀⠀⠀   Дубляжі файлів зникнуть,",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠉⠛⠿⣷⣶⣤⣤⣄⣰⣄⠀⠀⠉⠉⠉⠁⠀⢀⣀⣠⣄⣀⡀⠀⠉⠉⠉⠀⠀⢀⣠⣾⣥⣤⣤⣤⣶⣶⡿⠿⠛⠉⠀⠀⠀⠀⠀⠀⠀   як федерація їхня, назавжди.",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠉⢻⣿⠛⢿⣷⣦⣤⣴⣶⣶⣦⣤⣤⣤⣤⣬⣥⡴⠶⠾⠿⠿⠿⠿⠛⢛⣿⣿⣿⣯⡉⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⣿⣧⡀⠈⠉⠀⠈⠁⣾⠛⠉⠉⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣀⣴⣿⠟⠉⣹⣿⣇⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀                                               Йода",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⣸⣿⣿⣦⣀⠀⠀⠀⢻⡀⠀⠀⠀⠀⠀⠀⠀⢀⣠⣤⣶⣿⠋⣿⠛⠃⠀⣈⣿⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⣿⡿⢿⡀⠈⢹⡿⠶⣶⣼⡇⠀⢀⣀⣀⣤⣴⣾⠟⠋⣡⣿⡟⠀⢻⣶⠶⣿⣿⠛⠋⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠘⣿⣷⡈⢿⣦⣸⠇⢀⡿⠿⠿⡿⠿⠿⣿⠛⠋⠁⠀⣴⠟⣿⣧⡀⠈⢁⣰⣿⠏⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢸⣿⢻⣦⣈⣽⣀⣾⠃⠀⢸⡇⠀⢸⡇⠀⢀⣠⡾⠋⢰⣿⣿⣿⣿⡿⠟⠋⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠘⠿⢿⣿⣿⡟⠛⠃⠀⠀⣾⠀⠀⢸⡇⠐⠿⠋⠀⠀⣿⢻⣿⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢸⣿⠁⢀⡴⠋⠀⣿⠀⠀⢸⠇⠀⠀⠀⠀⠀⠁⢸⣿⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⣿⡿⠟⠋⠀⠀⠀⣿⠀⠀⣸⠀⠀⠀⠀⠀⠀⠀⢸⣿⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢸⣿⣁⣀⠀⠀⠀⠀⣿⡀⠀⣿⠀⠀⠀⠀⠀⠀⢀⣈⣿⣿⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀",
			"⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠘⠛⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠿⠟⠛⠋⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀"
		};

			Console.ForegroundColor = ConsoleColor.Green;
			foreach (string line in yodaArt) {
				Console.WriteLine(line);
			}
			Console.ResetColor();
		}
	}
}