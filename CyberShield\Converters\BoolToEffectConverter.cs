using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace CyberShield.Converters {
	public class BoolToEffectConverter : IValueConverter {
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture) {
			if (value is bool isScanning && isScanning) {
				return new DropShadowEffect {
					Color = (Color)ColorConverter.ConvertFromString("#1565C0"),
					Direction = 0,
					ShadowDepth = 0,
					BlurRadius = 15,
					Opacity = 0.5
				};
			}
			return null;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) {
			throw new NotImplementedException();
		}
	}
}
