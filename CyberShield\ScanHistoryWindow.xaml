<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="CyberShield.ScanHistoryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CyberShield"
        xmlns:converters="clr-namespace:CyberShield.Converters"
        mc:Ignorable="d"
        Title="История сканирований" Height="600" Width="900"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">
    <Window.Resources>
        <converters:BoolToScanTextConverter x:Key="BoolToScanTextConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToEffectConverter x:Key="BoolToEffectConverter"/>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <converters:NullToBoolConverter x:Key="NullToBoolConverter"/>

        <Style x:Key="RoundedButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#0D47A1"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#1976D2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True"><Setter Property="Background" Value="#1565C0"/><Setter Property="BorderBrush" Value="#42A5F5"/></Trigger>
                <Trigger Property="IsPressed" Value="True"><Setter Property="Background" Value="#0A3880"/><Setter Property="BorderBrush" Value="#1976D2"/></Trigger>
                <Trigger Property="IsEnabled" Value="False"><Setter Property="Opacity" Value="0.5"/><Setter Property="Background" Value="#9E9E9E"/><Setter Property="BorderBrush" Value="#BDBDBD"/></Trigger>
            </Style.Triggers>
        </Style>
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource RoundedButtonStyle}">
            <Setter Property="Background" Value="#C62828"/>
            <Setter Property="BorderBrush" Value="#E53935"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True"><Setter Property="Background" Value="#D32F2F"/><Setter Property="BorderBrush" Value="#EF5350"/></Trigger>
                <Trigger Property="IsPressed" Value="True"><Setter Property="Background" Value="#B71C1C"/><Setter Property="BorderBrush" Value="#E53935"/></Trigger>
                <Trigger Property="IsEnabled" Value="False"><Setter Property="Opacity" Value="0.5"/><Setter Property="Background" Value="#E57373"/><Setter Property="BorderBrush" Value="#EF9A9A"/></Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="68"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="3*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Водяной знак FSB на фоне (начинается под синей строкой с названием и заканчивается над синей строкой с разработчиками, заходит на секцию с кнопками) -->
        <Grid Grid.Row="1" Grid.RowSpan="3" Panel.ZIndex="1">
            <Viewbox Width="480" Height="480" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Image Source="pack://application:,,,/КиберЩит;component/Resources/fsb_logo.png"
                       Opacity="0.2"
                       Stretch="Uniform"/>
            </Viewbox>
        </Grid>


        <!-- Заголовок -->
        <Border Grid.Row="0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#0D47A1" Offset="0.0"/>
                    <GradientStop Color="#5CACEF" Offset="1.0"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="1"/>
                </Grid.RowDefinitions>
                <Grid Grid.Row="0" Margin="0,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,20,0">
                    </StackPanel>
                    <Border Grid.Column="1" CornerRadius="5" Padding="20,5" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <Image Source="pack://application:,,,/КиберЩит;component/Resources/logo.png" Width="64" Height="64" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBlock Text="КиберЩит" FontSize="32" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                            <Image Source="pack://application:,,,/КиберЩит;component/Resources/flag.png" Width="64" Height="64" VerticalAlignment="Center" Margin="15,0,0,0"/>
                        </StackPanel>
                    </Border>
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="20,0,0,0">
                    </StackPanel>
                </Grid>
                <Border Grid.Row="1" Background="#90CAF9" Height="1" Opacity="0.5"/>
            </Grid>
        </Border>
        <!-- Основной контент -->
        <Grid Grid.Row="2" Margin="20,10,20,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="White" BorderBrush="#1565C0" BorderThickness="1" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Border Grid.Row="0" Background="#1565C0" BorderThickness="0,0,0,1" BorderBrush="#42A5F5" Padding="15,10">
                        <TextBlock Text="История сканирований" FontSize="16" FontWeight="Medium" Foreground="White"/>
                    </Border>
                    <Grid Grid.Row="1">
                        <ListView x:Name="historyListView" ItemsSource="{Binding ScanHistory}" BorderThickness="0" Margin="0" Background="White" Foreground="#212121" SizeChanged="HistoryListView_SizeChanged">
                            <ListView.View>
                                <GridView>
                                    <GridViewColumn Header="Время" DisplayMemberBinding="{Binding ScanTime, StringFormat='{}{0:HH:mm:ss dd.MM.yyyy}'}" Width="130"/>
                                    <GridViewColumn Header="Директория сканирования" DisplayMemberBinding="{Binding ScanPath}" Width="Auto"/>
                                    <GridViewColumn Header="Просканировано файлов" DisplayMemberBinding="{Binding ScannedFiles}" Width="150"/>
                                    <GridViewColumn Header="Обнаружено угроз" DisplayMemberBinding="{Binding ThreatsFound}" Width="130"/>
                                </GridView>
                            </ListView.View>
                        </ListView>
                        <TextBlock Text="Записи не найдены" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" Foreground="#757575" Visibility="{Binding ScanHistory.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
                    </Grid>
                </Grid>
            </Border>
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                <Button Content="Удалить запись" Style="{StaticResource DangerButtonStyle}" Click="DeleteHistoryEntry_Click" IsEnabled="{Binding SelectedItem, ElementName=historyListView, Converter={StaticResource NullToBoolConverter}}" Margin="0,0,10,0"/>
                <Button Content="Закрыть" Style="{StaticResource RoundedButtonStyle}" Click="CloseButton_Click"/>
            </StackPanel>
        </Grid>
        <!-- Нижняя панель -->
        <Border Grid.Row="3" Background="#E3F2FD" BorderBrush="#90CAF9" BorderThickness="1" Margin="20,10,20,10" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Text="" FontSize="12" Foreground="#0D47A1" HorizontalAlignment="Left"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="Совместный продукт Лаборатории Касперского и ФСБ России" FontSize="12" Foreground="#0D47A1"/>
                    <TextBlock Text="© 2022-2025" FontSize="12" Foreground="#0D47A1" Margin="10,0,0,0"/>
                </StackPanel>
                <TextBlock Grid.Column="2" Text="Версия 1.4.128" FontSize="12" Foreground="#0D47A1" HorizontalAlignment="Right"/>
            </Grid>
        </Border>
    </Grid>
</Window>
