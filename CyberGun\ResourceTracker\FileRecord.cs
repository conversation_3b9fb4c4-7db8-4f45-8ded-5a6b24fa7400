using System;

namespace CyberGun.ResourceTracker {
	public class FileRecord {
		public string FileName { get; set; }
		public DateTime LastModified { get; set; }
		public long FileSize { get; set; }

		public override bool Equals(object obj) { // обязательно для корректного определения, отправлялся ли файл ранее
			if (obj is FileRecord other) {
				// сравниваем по имени файла, размеру и дате модификации
				return FileName == other.FileName &&
					   LastModified == other.LastModified &&
					   FileSize == other.FileSize;
			}
			return false;
		}

		public override int GetHashCode() { // обязательно для корректного определения, отправлялся ли файл ранее
			unchecked {
				int hash = 17;
				hash = hash * 23 + (FileName?.GetHashCode() ?? 0);
				hash = hash * 23 + LastModified.GetHashCode();
				hash = hash * 23 + FileSize.GetHashCode();
				return hash;
			}
		}
	}
}
