using System;
using System.Collections.Generic;
using System.Linq;

namespace CyberGun.ResourceTracker {
	public class SystemInfoData {
		public string ProgramInfo { get; set; }
		public DateTime StartTime { get; set; }
		public string ComputerName { get; set; }
		public string Username { get; set; }
		public string IpAddress { get; set; }
		public string Country { get; set; }
		public string City { get; set; }
		public string OSVersion { get; set; }
		public List<string> AntivirusList { get; set; }


		public string GetFullInfoString() {
			return $"📱 Программа: {ProgramInfo}\n" +
				   $"⏰ Время запуска: {StartTime:yyyy-MM-dd HH:mm:ss}\n" +
				   $"---\n" +
				   $"💻 Имя компьютера: {ComputerName}\n" +
				   $"👤 Пользователь: {Username}\n" +
				   $"---\n" +
				   $"🌐 IP адрес: {IpAddress}\n" +
				   $"🌍 Страна: {Country}\n" +
				   $"🏙 Город: {City}\n" +
				   $"---\n" +
				   $"🔧 Операционная система: {OSVersion}\n" +
				   $"🛡 Антивирус: {(AntivirusList.Any() ? string.Join(", ", AntivirusList) : "Не обнаружен")}";
		}

		public string GetShortInfoString() {
			return $"💻 {ComputerName} ({IpAddress})";
		}
	}
}
