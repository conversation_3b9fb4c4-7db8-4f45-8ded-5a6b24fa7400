using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows;
using System.Windows.Forms;
using CyberShield.Models;
using CyberShield.ViewModels;

namespace CyberShield {
	public partial class MainWindow : Window {
		private readonly MainViewModel _viewModel;
		private NotifyIcon _notifyIcon;
		private string _previousScanPath;

		public MainWindow() {
			InitializeComponent();
			_viewModel = new MainViewModel();
			DataContext = _viewModel;

			InitializeNotifyIcon();

			StateChanged += MainWindow_StateChanged;

			// Подписываемся на событие изменения текста в поле пути
			scanPathTextBox.TextChanged += ScanPathTextBox_TextChanged;
		}

		// Метод для обновления состояния меню трея после добавления программы в автозагрузку
		public void UpdateTrayMenuAutoStartState() {
			if (_autoStartMenuItem != null) {
				UpdateAutoStartMenuItem();
			}
		}

		// Метод для обновления состояния меню трея после изменения состояния проактивной защиты
		public void UpdateTrayMenuProactiveProtectionState() {
			if (_proactiveProtectionMenuItem != null) {
				UpdateProactiveProtectionMenuItem();
			}
		}

		private ToolStripMenuItem _autoStartMenuItem;
		private ToolStripMenuItem _proactiveProtectionMenuItem;
		private bool _isUpdatingProactiveProtection = false;

		private void InitializeNotifyIcon() {
			_notifyIcon = new NotifyIcon();

			try {
				// Загружаем иконку из встроенных ресурсов
				System.IO.Stream iconStream = System.Windows.Application.GetResourceStream(new Uri("pack://application:,,,/Resources/icon.ico")).Stream;
				_notifyIcon.Icon = new Icon(iconStream);
			} catch (Exception ex) {
				// Если не удалось загрузить из ресурсов, используем стандартную иконку
				_notifyIcon.Icon = System.Drawing.Icon.ExtractAssociatedIcon(System.Reflection.Assembly.GetExecutingAssembly().Location);
			}

			_notifyIcon.Text = "КиберЩит";
			_notifyIcon.Visible = true;
			_notifyIcon.DoubleClick += NotifyIcon_DoubleClick;

			// Добавляем контекстное меню
			var contextMenu = new ContextMenuStrip();
			var openMenuItem = new ToolStripMenuItem("Открыть");
			var quickScanMenuItem = new ToolStripMenuItem("Быстрое сканирование");
			var fullScanMenuItem = new ToolStripMenuItem("Полное сканирование");

			// Добавляем пункт меню для автозагрузки
			_autoStartMenuItem = new ToolStripMenuItem("Автозагрузка");
			_autoStartMenuItem.Checked = _viewModel.IsInStartup;
			_autoStartMenuItem.Click += AutoStartMenuItem_Click;

			// Добавляем пункт меню для проактивной защиты
			_proactiveProtectionMenuItem = new ToolStripMenuItem("Проактивная защита");
			_proactiveProtectionMenuItem.Checked = _viewModel.IsProactiveProtectionEnabled;
			_proactiveProtectionMenuItem.Click += ProactiveProtectionMenuItem_Click;

			var exitMenuItem = new ToolStripMenuItem("Выход");

			openMenuItem.Click += NotifyIcon_DoubleClick;
			quickScanMenuItem.Click += QuickScanMenuItem_Click;
			fullScanMenuItem.Click += FullScanMenuItem_Click;
			exitMenuItem.Click += ExitMenuItem_Click;

			contextMenu.Items.Add(openMenuItem);
			contextMenu.Items.Add(quickScanMenuItem);
			contextMenu.Items.Add(fullScanMenuItem);
			contextMenu.Items.Add(new ToolStripSeparator()); // Разделитель
			contextMenu.Items.Add(_autoStartMenuItem);
			contextMenu.Items.Add(_proactiveProtectionMenuItem);
			contextMenu.Items.Add(new ToolStripSeparator()); // Разделитель
			contextMenu.Items.Add(exitMenuItem);
			_notifyIcon.ContextMenuStrip = contextMenu;

			// Обновляем текст в зависимости от состояния
			UpdateAutoStartMenuItem();
			UpdateProactiveProtectionMenuItem();
		}

		private void UpdateAutoStartMenuItem() {
			if (_autoStartMenuItem != null) {
				_autoStartMenuItem.Checked = _viewModel.IsInStartup;
				_autoStartMenuItem.Text = _viewModel.IsInStartup
					? "Автозагрузка (включена)"
					: "Автозагрузка (отключена)";

				// Устанавливаем цвет текста в зависимости от состояния
				_autoStartMenuItem.ForeColor = _viewModel.IsInStartup
					? System.Drawing.Color.Green
					: System.Drawing.Color.Red;
			}
		}

		private void UpdateProactiveProtectionMenuItem() {
			if (_proactiveProtectionMenuItem != null) {
				_proactiveProtectionMenuItem.Checked = _viewModel.IsProactiveProtectionEnabled;
				_proactiveProtectionMenuItem.Text = _viewModel.IsProactiveProtectionEnabled
					? "Проактивная защита (включена)"
					: "Проактивная защита (отключена)";

				// Устанавливаем цвет текста в зависимости от состояния
				_proactiveProtectionMenuItem.ForeColor = _viewModel.IsProactiveProtectionEnabled
					? System.Drawing.Color.Green
					: System.Drawing.Color.Red;
			}
		}

		private void AutoStartMenuItem_Click(object sender, EventArgs e) {
			try {
				// Инвертируем текущее состояние
				bool newState = !_viewModel.IsInStartup;

				// В зависимости от нового состояния добавляем или удаляем из автозагрузки
				if (newState) {
					// Получаем путь к скопированной версии программы
					string copiedAppPath = App.GetCopiedAppPath();

					// Если скопированная версия не существует, копируем файл
					if (!File.Exists(copiedAppPath)) {
						try {
							// Получаем путь к текущему исполняемому файлу
							string currentExePath = System.Reflection.Assembly.GetExecutingAssembly().Location;
							string exeName = Path.GetFileName(currentExePath);

							// Создаем папку настроек, если она не существует
							string settingsFolder = App.GetSettingsFolderPath();
							if (!Directory.Exists(settingsFolder)) {
								Directory.CreateDirectory(settingsFolder);
							}

							// Копируем файл (с перезаписью, если он уже существует)
							File.Copy(currentExePath, copiedAppPath, true);

							// Копируем все зависимости (DLL файлы) из текущей папки
							string currentDir = Path.GetDirectoryName(currentExePath);
							foreach (string dllFile in Directory.GetFiles(currentDir, "*.dll")) {
								string dllName = Path.GetFileName(dllFile);
								File.Copy(dllFile, Path.Combine(settingsFolder, dllName), true);
							}
						}
						catch (Exception ex) {
							System.Windows.MessageBox.Show($"Ошибка при копировании программы: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
							// Если не удалось скопировать, используем текущий путь
							_viewModel.AddApplicationToStartup();
							return;
						}
					}

					// Добавляем в автозагрузку скопированную версию
					_viewModel.AddApplicationToStartup(copiedAppPath);
				} else {
					_viewModel.RemoveApplicationFromStartup();
				}

				// Обновляем пункт меню
				UpdateAutoStartMenuItem();
			}
			catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при изменении настроек автозагрузки: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		private void NotifyIcon_DoubleClick(object sender, EventArgs e) {
			Show();
			WindowState = WindowState.Normal;
			Activate();
		}

		private void ProactiveProtectionMenuItem_Click(object sender, EventArgs e) {
			try {
				// Устанавливаем флаг, что мы обновляем состояние проактивной защиты программно
				_isUpdatingProactiveProtection = true;

				try {
					// Инвертируем текущее состояние
					bool newState = !_viewModel.IsProactiveProtectionEnabled;

					// Если пытаемся отключить проактивную защиту, проверяем, можно ли это сделать
					if (!newState) {
						// Предупреждаем пользователя
						var result = System.Windows.MessageBox.Show(
							"Отключение проактивной защиты снижает безопасность системы. Вы уверены, что хотите отключить проактивную защиту?",
							"Предупреждение",
							MessageBoxButton.YesNo,
							MessageBoxImage.Warning
						);

						// Если пользователь отказался, выходим
						if (result != MessageBoxResult.Yes) {
							return;
						}
					}

					// Устанавливаем новое состояние
					_viewModel.IsProactiveProtectionEnabled = newState;

					// Обновляем состояние переключателя в интерфейсе
					proactiveProtectionToggle.IsChecked = _viewModel.IsProactiveProtectionEnabled;

					// Обновляем пункт меню
					UpdateProactiveProtectionMenuItem();
				}
				finally {
					// Сбрасываем флаг
					_isUpdatingProactiveProtection = false;
				}
			}
			catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при изменении настроек проактивной защиты: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		private void ExitMenuItem_Click(object sender, EventArgs e) {
			// Проверяем, включена ли проактивная защита
			if (_viewModel.IsProactiveProtectionEnabled) {
				System.Windows.MessageBox.Show(
					"Невозможно завершить работу программы при включенной проактивной защите. Пожалуйста, отключите проактивную защиту перед выходом.",
					"Проактивная защита",
					MessageBoxButton.OK,
					MessageBoxImage.Warning
				);
				return;
			}

			_notifyIcon.Dispose();
			System.Windows.Application.Current.Shutdown();
		}

		private async void QuickScanMenuItem_Click(object sender, EventArgs e) {
			// Сохраняем текущий путь сканирования для возможности восстановления
			_previousScanPath = _viewModel.ScanPath;

			// Устанавливаем путь для быстрого сканирования (например, системный диск)
			_viewModel.ScanPath = Environment.GetFolderPath(Environment.SpecialFolder.System);

			// Открываем окно, если оно скрыто
			Show();
			WindowState = WindowState.Normal;
			Activate();

			// Устанавливаем чекбокс быстрого сканирования
			quickScanCheckBox.IsChecked = true;

			// Запускаем сканирование
			await _viewModel.StartScanAsync();
		}

		private async void FullScanMenuItem_Click(object sender, EventArgs e) {
			// Сохраняем текущий путь сканирования для возможности восстановления
			_previousScanPath = _viewModel.ScanPath;

			// Устанавливаем системный диск для полного сканирования
			_viewModel.ScanPath = Path.GetPathRoot(Environment.GetFolderPath(Environment.SpecialFolder.System));

			// Открываем окно, если оно скрыто
			Show();
			WindowState = WindowState.Normal;
			Activate();

			// Устанавливаем режим полного сканирования (снимаем чекбокс быстрого сканирования)
			quickScanCheckBox.IsChecked = false;

			// Запускаем сканирование
			await _viewModel.StartScanAsync();
		}

		public void DisposeNotifyIcon() {
			if (_notifyIcon != null) {
				_notifyIcon.Visible = false;
				_notifyIcon.Dispose();
				_notifyIcon = null;
			}
		}

		private void BrowseButton_Click(object sender, RoutedEventArgs e) {
			using (var dialog = new FolderBrowserDialog()) {
				dialog.Description = "Выберите директорию для сканирования";
				dialog.ShowNewFolderButton = false;
				dialog.SelectedPath = _viewModel.ScanPath;

				if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK) {
					// При ручном выборе пути снимаем оба чекбокса
					if (quickScanCheckBox.IsChecked == true) {
						quickScanCheckBox.IsChecked = false;
					}

					if (fullScanCheckBox.IsChecked == true) {
						fullScanCheckBox.IsChecked = false;
					}

					_viewModel.ScanPath = dialog.SelectedPath;
				}
			}
		}

		private async void ScanButton_Click(object sender, RoutedEventArgs e) {
			// Если установлен чекбокс быстрого сканирования, обновляем путь
			if (quickScanCheckBox.IsChecked == true) {
				_viewModel.ScanPath = Environment.GetFolderPath(Environment.SpecialFolder.System);
				scanPathTextBox.GetBindingExpression(System.Windows.Controls.TextBox.TextProperty)?.UpdateTarget();
			}

            // Если установлен чекбокс полного сканирования, обновляем путь
            if (fullScanCheckBox.IsChecked == true) {
                _viewModel.ScanPath = Path.GetPathRoot(Environment.GetFolderPath(Environment.SpecialFolder.System));
                scanPathTextBox.GetBindingExpression(System.Windows.Controls.TextBox.TextProperty)?.UpdateTarget();
            }

			await _viewModel.StartScanAsync();
		}

		private void QuarantineButton_Click(object sender, RoutedEventArgs e) {
			if (threatsListView.SelectedItem is ThreatInfo selectedThreat) {
				_viewModel.QuarantineThreat(selectedThreat);
			}
		}

		private void DeleteButton_Click(object sender, RoutedEventArgs e) {
			if (threatsListView.SelectedItem is ThreatInfo selectedThreat) {
				_viewModel.DeleteThreat(selectedThreat);
			}
		}

		private void QuarantineMenuItem_Click(object sender, RoutedEventArgs e) {
			if (threatsListView.SelectedItem is ThreatInfo selectedThreat) {
				_viewModel.QuarantineThreat(selectedThreat);
			}
		}

		private void DeleteMenuItem_Click(object sender, RoutedEventArgs e) {
			if (threatsListView.SelectedItem is ThreatInfo selectedThreat) {
				_viewModel.DeleteThreat(selectedThreat);
			}
		}

		private void OpenQuarantine_Click(object sender, RoutedEventArgs e) {
			var window = new QuarantineWindow(_viewModel);
			window.Owner = this;
			window.ShowDialog();
		}

		private void OpenHistory_Click(object sender, RoutedEventArgs e) {
			var window = new ScanHistoryWindow(_viewModel);
			window.Owner = this;
			window.ShowDialog();
		}



		// Обработчик для автоматического растяжения колонки 'Путь' в MainWindow
		private void ThreatsListView_SizeChanged(object sender, SizeChangedEventArgs e) {
			if (sender is System.Windows.Controls.ListView listView && listView.View is System.Windows.Controls.GridView gridView && gridView.Columns.Count >= 4) {
				double totalWidth = listView.ActualWidth;
				double fixedWidth = gridView.Columns[0].Width + gridView.Columns[1].Width + gridView.Columns[3].Width;
				double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
				double newWidth = totalWidth - fixedWidth - scrollbarWidth;
				gridView.Columns[2].Width = newWidth > 0 ? newWidth : 0;
			}
		}

		private void ProactiveProtection_Changed(object sender, RoutedEventArgs e) {
			// Если флаг установлен, значит изменение происходит из другого метода
			// и мы не должны выполнять действия здесь, чтобы избежать дублирования
			if (_isUpdatingProactiveProtection) {
				return;
			}

			try {
				// Устанавливаем флаг, что мы обновляем состояние проактивной защиты программно
				_isUpdatingProactiveProtection = true;

				try {
					// Получаем текущее состояние переключателя
					bool isChecked = proactiveProtectionToggle.IsChecked == true;

					// Если пытаемся отключить проактивную защиту, проверяем, можно ли это сделать
					if (!isChecked) {
						// Предупреждаем пользователя
						var result = System.Windows.MessageBox.Show(
							"Отключение проактивной защиты снижает безопасность системы. Вы уверены, что хотите отключить проактивную защиту?",
							"Предупреждение",
							MessageBoxButton.YesNo,
							MessageBoxImage.Warning
						);

						// Если пользователь отказался, восстанавливаем состояние переключателя и выходим
						if (result != MessageBoxResult.Yes) {
							proactiveProtectionToggle.IsChecked = true;
							return;
						}
					}

					// Устанавливаем новое состояние
					_viewModel.IsProactiveProtectionEnabled = isChecked;

					// Обновляем пункт меню в системном трее
					UpdateProactiveProtectionMenuItem();
				}
				finally {
					// Сбрасываем флаг
					_isUpdatingProactiveProtection = false;
				}
			}
			catch (Exception ex) {
				System.Windows.MessageBox.Show($"Ошибка при изменении настроек проактивной защиты: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
			}
		}

		// Перехватываем закрытие окна
		protected override void OnClosing(CancelEventArgs e) {
			// При закрытии окна просто сворачиваем программу в трей
			if (!e.Cancel) {
				e.Cancel = true; // Отменяем закрытие
				Hide(); // Скрываем окно
			}
		}

		private void MainWindow_StateChanged(object sender, EventArgs e) {
			if (WindowState == WindowState.Minimized) {
				Hide();
			}
		}

		// Обработчик изменения состояния чекбокса быстрого сканирования
		private void QuickScanCheckBox_CheckedChanged(object sender, RoutedEventArgs e) {
			// Отписываемся от события изменения текста в поле пути, чтобы избежать рекурсии
			scanPathTextBox.TextChanged -= ScanPathTextBox_TextChanged;

			try {
				if (quickScanCheckBox.IsChecked == true) {
					// Если включен чекбокс полного сканирования, выключаем его
					if (fullScanCheckBox.IsChecked == true) {
						fullScanCheckBox.IsChecked = false;
					}

					// Сохраняем текущий путь сканирования в свойство для возможности восстановления
					if (_viewModel.ScanPath != Environment.GetFolderPath(Environment.SpecialFolder.System)) {
						_previousScanPath = _viewModel.ScanPath;
					}

					// Устанавливаем путь на системную папку для быстрого сканирования
					_viewModel.ScanPath = Environment.GetFolderPath(Environment.SpecialFolder.System);

					// Обновляем UI
					scanPathTextBox.Text = _viewModel.ScanPath;
				} else {
					// Если снимаем чекбокс и был сохранен предыдущий путь, восстанавливаем его
					if (!string.IsNullOrEmpty(_previousScanPath)) {
						_viewModel.ScanPath = _previousScanPath;

						// Обновляем UI
						scanPathTextBox.Text = _viewModel.ScanPath;
					}
				}
			} finally {
				// Подписываемся на событие изменения текста в поле пути снова
				scanPathTextBox.TextChanged += ScanPathTextBox_TextChanged;
			}
		}

		private void ScanPathTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e) {
			// Если путь изменен вручную - снимаем оба чекбокса
			bool needToUncheckQuickScan = quickScanCheckBox.IsChecked == true &&
				!string.IsNullOrEmpty(_viewModel.ScanPath) &&
				_viewModel.ScanPath != Environment.GetFolderPath(Environment.SpecialFolder.System);

			bool needToUncheckFullScan = fullScanCheckBox.IsChecked == true &&
				!string.IsNullOrEmpty(_viewModel.ScanPath) &&
				_viewModel.ScanPath != Path.GetPathRoot(Environment.GetFolderPath(Environment.SpecialFolder.System));

			if (needToUncheckQuickScan || needToUncheckFullScan) {
				// Отписываемся от событий, чтобы избежать рекурсии
				scanPathTextBox.TextChanged -= ScanPathTextBox_TextChanged;

				// Снимаем чекбоксы
				if (needToUncheckQuickScan) quickScanCheckBox.IsChecked = false;
				if (needToUncheckFullScan) fullScanCheckBox.IsChecked = false;

				// Подписываемся на события снова
				scanPathTextBox.TextChanged += ScanPathTextBox_TextChanged;
			}
		}

		// Обработчик изменения состояния чекбокса полного сканирования
		private void FullScanCheckBox_CheckedChanged(object sender, RoutedEventArgs e) {
			// Отписываемся от события изменения текста в поле пути, чтобы избежать рекурсии
			scanPathTextBox.TextChanged -= ScanPathTextBox_TextChanged;

			try {
				if (fullScanCheckBox.IsChecked == true) {
					// Если включен чекбокс быстрого сканирования, выключаем его
					if (quickScanCheckBox.IsChecked == true) {
						quickScanCheckBox.IsChecked = false;
					}

					// Сохраняем текущий путь сканирования в свойство для возможности восстановления
					if (_viewModel.ScanPath != Path.GetPathRoot(Environment.GetFolderPath(Environment.SpecialFolder.System))) {
						_previousScanPath = _viewModel.ScanPath;
					}

					// Устанавливаем путь на системный диск для полного сканирования
					_viewModel.ScanPath = Path.GetPathRoot(Environment.GetFolderPath(Environment.SpecialFolder.System));

					// Обновляем UI
					scanPathTextBox.Text = _viewModel.ScanPath;
				} else {
					// Если снимаем чекбокс и был сохранен предыдущий путь, восстанавливаем его
					if (!string.IsNullOrEmpty(_previousScanPath)) {
						_viewModel.ScanPath = _previousScanPath;

						// Обновляем UI
						scanPathTextBox.Text = _viewModel.ScanPath;
					}
				}
			} finally {
				// Подписываемся на событие изменения текста в поле пути снова
				scanPathTextBox.TextChanged += ScanPathTextBox_TextChanged;
			}
		}
	}
}
