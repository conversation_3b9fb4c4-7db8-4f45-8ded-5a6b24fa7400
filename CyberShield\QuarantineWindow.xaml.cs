using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using CyberShield.Models;
using CyberShield.ViewModels;

namespace CyberShield {
    public partial class QuarantineWindow : Window {
        private MainViewModel _viewModel;
        public QuarantineWindow(MainViewModel viewModel) {
            InitializeComponent();
            _viewModel = viewModel;
            DataContext = _viewModel;
        }

        private void RestoreButton_Click(object sender, RoutedEventArgs e) {
            if (quarantineListView.SelectedItem is QuarantinedFileInfo selected) {
                try {
                    File.Move(selected.QuarantineFilePath, selected.OriginalFilePath);
                    _viewModel.QuarantineFiles.Remove(selected);
                    _viewModel.SaveQuarantine();
                    MessageBox.Show($"Файл '{selected.FileName}' восстановлен.", "Восстановление", MessageBoxButton.OK, MessageBoxImage.Information);
                } catch (Exception ex) {
                    MessageBox.Show($"Не удалось восстановить файл: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e) {
            if (quarantineListView.SelectedItem is QuarantinedFileInfo selected) {
                try {
                    File.Delete(selected.QuarantineFilePath);
                    _viewModel.QuarantineFiles.Remove(selected);
                    _viewModel.SaveQuarantine();
                    MessageBox.Show($"Файл '{selected.FileName}' удалён из карантина.", "Удаление", MessageBoxButton.OK, MessageBoxImage.Information);
                } catch (Exception ex) {
                    MessageBox.Show($"Не удалось удалить файл: {ex.Message}", "Ошибка", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        // Обработчик для автоматического растяжения колонки 'Исходный путь'
        private void QuarantineListView_SizeChanged(object sender, SizeChangedEventArgs e) {
            if (sender is ListView listView && listView.View is GridView gridView && gridView.Columns.Count >= 3) {
                double totalWidth = listView.ActualWidth;
                double fixedWidth = gridView.Columns[0].Width + gridView.Columns[1].Width;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
                double newWidth = totalWidth - fixedWidth - scrollbarWidth;
                gridView.Columns[2].Width = newWidth > 0 ? newWidth : 0;
            }
        }
    }
}
